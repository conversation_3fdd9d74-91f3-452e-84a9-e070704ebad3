import {
  inject,
  signal,
  computed,
  DestroyRef,
  ChangeDetectorRef
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ThemeService } from '../services/theme-service/theme.service';
import { createLogger } from './logger';

/**
 * Functional Theme Manager Utility
 *
 * Provides a lightweight, functional approach to theme management
 * for components that prefer composition over inheritance.
 *
 * Features:
 * - Automatic subscription cleanup using DestroyRef
 * - OnPush change detection compatibility
 * - Type-safe theme asset resolution
 * - Angular 19+ signals support
 *
 * Usage:
 * ```typescript
 * export class MyComponent {
 *   private themeManager = createThemeManager();
 *
 *   constructor() {
 *     // Access theme via this.themeManager.theme()
 *     // Handle theme changes via this.themeManager.onThemeChange()
 *   }
 * }
 * ```
 *
 * @requires Must be called within an Angular injection context (component constructor)
 */

export interface ThemeManager {
  readonly theme: () => 'light' | 'dark';
  readonly isDarkTheme: () => boolean;
  readonly isLightTheme: () => boolean;
  readonly themeClass: () => string;
  readonly onThemeChange: (callback: (theme: 'light' | 'dark') => void) => void;
  readonly getThemeAsset: (basePath: string, extension?: string) => string;
  readonly getThemeValue: <T>(lightValue: T, darkValue: T) => T;
  readonly toggleTheme: () => void;
  readonly setTheme: (theme: 'light' | 'dark') => void;
}

/**
 * Create a theme manager instance for a component
 * @throws Error if called outside of Angular injection context
 */
export function createThemeManager(): ThemeManager {
  try {
    const themeService = inject(ThemeService);
    const destroyRef = inject(DestroyRef);
    const cdr = inject(ChangeDetectorRef);
    const logger = createLogger('ThemeManager');

  // Reactive theme state using signals
  const theme = signal<'light' | 'dark'>(themeService.getCurrentTheme());

  // Computed values
  const isDarkTheme = computed(() => theme() === 'dark');
  const isLightTheme = computed(() => theme() === 'light');
  const themeClass = computed(() => `${theme()}-theme`);

  // Theme change callbacks
  const themeChangeCallbacks = new Set<(theme: 'light' | 'dark') => void>();

  // Initialize theme subscription
  themeService.themeObservable
    .pipe(takeUntilDestroyed(destroyRef))
    .subscribe((newTheme: 'light' | 'dark') => {
      theme.set(newTheme);
      cdr.markForCheck();

      // Execute all registered callbacks
      themeChangeCallbacks.forEach(callback => {
        try {
          callback(newTheme);
        } catch (error) {
          logger.error('Error in theme change callback:', error);
        }
      });
    });

  return {
    theme: () => theme(),
    isDarkTheme: () => isDarkTheme(),
    isLightTheme: () => isLightTheme(),
    themeClass: () => themeClass(),

    onThemeChange: (callback: (theme: 'light' | 'dark') => void) => {
      themeChangeCallbacks.add(callback);
    },

    getThemeAsset: (basePath: string, extension: string = 'svg') => {
      return `${basePath}-${theme()}.${extension}`;
    },

    getThemeValue: <T>(lightValue: T, darkValue: T): T => {
      return isDarkTheme() ? darkValue : lightValue;
    },

    toggleTheme: () => {
      themeService.toggleTheme();
    },

    setTheme: (newTheme: 'light' | 'dark') => {
      themeService.setTheme(newTheme);
    }
  };
  } catch (error) {
    const fallbackLogger = createLogger('ThemeManager');
    fallbackLogger.error('Failed to create theme manager. Must be called within Angular injection context:', error);
    throw new Error('createThemeManager() must be called within an Angular injection context (component constructor)');
  }
}

/**
 * Simplified theme manager for components that only need basic theme access
 */
export function createSimpleThemeManager() {
  const themeService = inject(ThemeService);
  const destroyRef = inject(DestroyRef);
  const cdr = inject(ChangeDetectorRef);

  const theme = signal<'light' | 'dark'>(themeService.getCurrentTheme());

  themeService.themeObservable
    .pipe(takeUntilDestroyed(destroyRef))
    .subscribe((newTheme: 'light' | 'dark') => {
      theme.set(newTheme);
      cdr.markForCheck();
    });

  return {
    theme: () => theme(),
    isDark: computed(() => theme() === 'dark'),
    isLight: computed(() => theme() === 'light')
  };
}

/**
 * Theme-aware asset resolver utility
 * Must be instantiated within an injection context (component constructor)
 */
export class ThemeAssetResolver {
  private themeService: ThemeService;

  constructor() {
    this.themeService = inject(ThemeService);
  }

  /**
   * Get theme-specific asset path
   */
  getAsset(basePath: string, extension: string = 'svg'): string {
    return `${basePath}-${this.themeService.getCurrentTheme()}.${extension}`;
  }

  /**
   * Get multiple theme-specific assets with type safety
   */
  getAssets<T extends Record<string, string>>(assets: T): Record<keyof T, string> {
    const result: Record<keyof T, string> = {} as Record<keyof T, string>;
    const currentTheme = this.themeService.getCurrentTheme();

    for (const [key, basePath] of Object.entries(assets)) {
      result[key as keyof T] = `${basePath}-${currentTheme}.svg`;
    }

    return result;
  }

  /**
   * Get theme-conditional CSS classes
   */
  getClasses(...baseClasses: string[]): string[] {
    const currentTheme = this.themeService.getCurrentTheme();
    return baseClasses.map(cls => `${cls}-${currentTheme}`);
  }
}

/**
 * Hook-style theme manager for functional components
 */
export function useTheme() {
  return createThemeManager();
}

/**
 * Functional theme asset utilities (no class instantiation required)
 */
export function createThemeAssetResolver() {
  const themeService = inject(ThemeService);

  return {
    getAsset: (basePath: string, extension: string = 'svg') => {
      return `${basePath}-${themeService.getCurrentTheme()}.${extension}`;
    },

    getAssets: <T extends Record<string, string>>(assets: T): Record<keyof T, string> => {
      const result: Record<keyof T, string> = {} as Record<keyof T, string>;
      const currentTheme = themeService.getCurrentTheme();

      for (const [key, basePath] of Object.entries(assets)) {
        result[key as keyof T] = `${basePath}-${currentTheme}.svg`;
      }

      return result;
    },

    getClasses: (...baseClasses: string[]) => {
      const currentTheme = themeService.getCurrentTheme();
      return baseClasses.map(cls => `${cls}-${currentTheme}`);
    }
  };
}

/**
 * Theme configuration helper types and utilities
 */
export type ThemeConfig<T> = {
  light: T;
  dark: T;
};

export function createThemeConfig<T>(light: T, dark: T): ThemeConfig<T> {
  return { light, dark };
}

export function resolveThemeConfig<T>(
  config: ThemeConfig<T>,
  theme: 'light' | 'dark'
): T {
  return config[theme];
}

/**
 * Theme-specific color utilities
 */
export const THEME_COLORS = {
  text: createThemeConfig('#1D1D1D', '#ffffff'),
  description: createThemeConfig('#595959', '#cccccc'),
  border: createThemeConfig('rgba(0, 0, 0, 0.1)', 'rgba(255, 255, 255, 0.1)'),
  background: createThemeConfig('#ffffff', '#1a1a1a'),
  surface: createThemeConfig('#f8f9fa', '#2d2d2d')
} as const;

/**
 * Get theme-specific color value
 */
export function getThemeColor(
  colorKey: keyof typeof THEME_COLORS,
  theme: 'light' | 'dark'
): string {
  return resolveThemeConfig(THEME_COLORS[colorKey], theme);
}
