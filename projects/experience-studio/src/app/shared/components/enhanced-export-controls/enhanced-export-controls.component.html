<!-- Enhanced Export Controls -->
<div class="enhanced-export-controls"
     [class.disabled]="disabled || !hasTarget"
     [class.theme-dark]="theme === 'dark'"
     [class.orientation-vertical]="orientation === 'vertical'"
     [class.loading]="isAnyLoading"
     (click)="closeDropdowns()">

  <!-- Screenshot Controls Group -->
  <div class="export-group screenshot-group">
    <div class="group-header" *ngIf="showLabels">
      <span class="group-title">Screenshots</span>
    </div>
    
    <!-- Screenshot Button with Dropdown -->
    <div class="button-dropdown" 
         [class.active]="showScreenshotOptions">
      
      <!-- Main Screenshot Button -->
      <button class="export-button screenshot-button"
              [disabled]="disabled || !hasTarget || isScreenshotLoading"
              (click)="toggleScreenshotOptions(); $event.stopPropagation()"
              [title]="'Capture screenshot of the current design'">
        
        <!-- Loading Spinner -->
        <div class="button-spinner" *ngIf="isScreenshotLoading">
          <div class="spinner"></div>
        </div>
        
        <!-- Screenshot Icon -->
        <svg class="button-icon" *ngIf="!isScreenshotLoading" 
             width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M9 2C8.45 2 8 2.45 8 3S8.45 4 9 4H15C15.55 4 16 3.55 16 3S15.55 2 15 2H9ZM4 5V19C4 20.1 4.9 21 6 21H18C19.1 21 20 20.1 20 19V5C20 3.9 19.1 3 18 3H17V4C17 4.55 16.55 5 16 5H8C7.45 5 7 4.55 7 4V3H6C4.9 3 4 3.9 4 5ZM12 7C14.76 7 17 9.24 17 12S14.76 17 12 17S7 14.76 7 12S9.24 7 12 7ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z" 
                fill="currentColor"/>
        </svg>
        
        <!-- Button Text -->
        <span class="button-text" *ngIf="showLabels">Screenshot</span>
        
        <!-- Dropdown Arrow -->
        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
          <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
        </svg>
      </button>
      
      <!-- Screenshot Options Dropdown -->
      <div class="dropdown-menu" *ngIf="showScreenshotOptions" (click)="$event.stopPropagation()">
        <button class="dropdown-item"
                [disabled]="disabled || !hasTarget || isScreenshotLoading"
                (click)="onStandardScreenshot()">
          <svg class="item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
            <rect x="7" y="7" width="10" height="10" rx="1" fill="currentColor"/>
          </svg>
          <span>Standard Screenshot</span>
          <small>Visible area only</small>
        </button>
        
        <button class="dropdown-item"
                [disabled]="disabled || !hasTarget || isScreenshotLoading"
                (click)="onExtendedScreenshot()">
          <svg class="item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
            <rect x="7" y="7" width="10" height="6" rx="1" fill="currentColor"/>
            <rect x="7" y="15" width="10" height="2" rx="1" fill="currentColor"/>
          </svg>
          <span>Extended Screenshot</span>
          <small>Full scrollable content</small>
        </button>
      </div>
    </div>
  </div>

  <!-- Export Controls Group -->
  <div class="export-group export-group-main">
    <div class="group-header" *ngIf="showLabels">
      <span class="group-title">Export</span>
    </div>
    
    <!-- Figma Export Button -->
    <button class="export-button figma-button"
            [disabled]="disabled || !hasTarget || isFigmaLoading"
            (click)="onFigmaExport()"
            [title]="'Export design to Figma-compatible format'">
      
      <!-- Loading Spinner -->
      <div class="button-spinner" *ngIf="isFigmaLoading">
        <div class="spinner"></div>
      </div>
      
      <!-- Figma Icon -->
      <svg class="button-icon" *ngIf="!isFigmaLoading" 
           width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M8 2C6.9 2 6 2.9 6 4S6.9 6 8 6H10V2H8ZM10 8H8C6.9 8 6 8.9 6 10S6.9 12 8 12H10V8ZM10 14H8C6.9 14 6 14.9 6 16S6.9 18 8 18S10 17.1 10 16V14ZM12 2V6H14C15.1 6 16 5.1 16 4S15.1 2 14 2H12ZM14 8H12V12H14C15.1 12 16 11.1 16 10S15.1 8 14 8Z" 
              fill="currentColor"/>
      </svg>
      
      <!-- Button Text -->
      <span class="button-text" *ngIf="showLabels">Copy to Figma</span>
    </button>
    
    <!-- HTML Export Dropdown -->
    <div class="button-dropdown" 
         [class.active]="showExportOptions">
      
      <!-- Main HTML Button -->
      <button class="export-button html-button"
              [disabled]="disabled || !hasTarget || isHtmlLoading"
              (click)="toggleExportOptions(); $event.stopPropagation()"
              [title]="'View or download HTML code'">
        
        <!-- Loading Spinner -->
        <div class="button-spinner" *ngIf="isHtmlLoading">
          <div class="spinner"></div>
        </div>
        
        <!-- HTML Icon -->
        <svg class="button-icon" *ngIf="!isHtmlLoading" 
             width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z" 
                fill="currentColor"/>
        </svg>
        
        <!-- Button Text -->
        <span class="button-text" *ngIf="showLabels">HTML Code</span>
        
        <!-- Dropdown Arrow -->
        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
          <path d="M7 10L12 15L17 10H7Z" fill="currentColor"/>
        </svg>
      </button>
      
      <!-- HTML Options Dropdown -->
      <div class="dropdown-menu" *ngIf="showExportOptions" (click)="$event.stopPropagation()">
        <button class="dropdown-item"
                [disabled]="disabled || !hasTarget || isHtmlLoading"
                (click)="onViewHTMLCode()">
          <svg class="item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5S21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z" 
                  fill="currentColor"/>
          </svg>
          <span>View HTML Code</span>
          <small>Open in modal with syntax highlighting</small>
        </button>
        
        <button class="dropdown-item"
                [disabled]="disabled || !hasTarget || isHtmlLoading"
                (click)="onDownloadHTML()">
          <svg class="item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20ZM8 15.01L9.41 16.42L11 14.84V19H13V14.84L14.59 16.43L16 15.01L12.01 11L8 15.01Z" 
                  fill="currentColor"/>
          </svg>
          <span>Download HTML File</span>
          <small>Save as standalone HTML file</small>
        </button>
      </div>
    </div>
  </div>

  <!-- Status Indicator -->
  <div class="status-indicator" *ngIf="isAnyLoading">
    <div class="status-spinner"></div>
    <span class="status-text">
      <span *ngIf="isScreenshotLoading">Capturing screenshot...</span>
      <span *ngIf="isFigmaLoading">Exporting to Figma...</span>
      <span *ngIf="isHtmlLoading">Processing HTML...</span>
    </span>
  </div>

  <!-- No Target Warning -->
  <div class="no-target-warning" *ngIf="!hasTarget && !disabled">
    <svg class="warning-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
      <path d="M1 21H23L12 2L1 21ZM13 18H11V16H13V18ZM13 14H11V10H13V14Z" fill="currentColor"/>
    </svg>
    <span>No content available for export</span>
  </div>
</div>
