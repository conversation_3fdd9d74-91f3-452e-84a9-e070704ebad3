// Vertical Stepper Component Styles
.vertical-stepper {
  width: 100%;
  margin: 16px auto;
  border-radius: 8px;
  box-sizing: border-box;
  animation: fadeIn 0.5s ease-in-out;

  // Theme-specific styles
  &.light,
  &.dark {
    background-color: var(--chat-window-card-bg-color) !important;
    box-shadow: none;
    backdrop-filter: blur(60px);
  }

  // Stepper item states
  .stepper-item {
    transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
    will-change: transform, opacity;
    transform: translateZ(0);

    &.hidden {
      display: none;
    }

    &.future {
      opacity: 0;
      height: 0;
      transform: translateY(-20px);
      overflow: hidden;
    }

    &.next {
      opacity: 0.6;
      pointer-events: none;
    }


    // Completed step styles
    &.completed {
      .step-circle {
        background-color: #9c27b0;
        border: none;
        color: white;
        transition: all 0.3s ease-out;
        box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
      }

      .step-line {
        background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%);
      }

      .step-title {
        transition: all 0.3s ease-out;

        .light & {
          color: var(--color-primary, #6b46c1);
        }

        .dark & {
          color: var(--color-primary-light, #9f7aea);
        }
      }

      &.in-progress-mode {
        .step-circle,
        .step-title {
          cursor: pointer !important;
          pointer-events: auto;
        }
      }
    }

    // Active step styles
    &.active {
      .step-circle {
        border: none;
        color: white;
        transition: all 0.3s ease-out;
        box-shadow: none;
      }

      .step-title {
        font-weight: 600;

        .light & {
          color: var(--color-primary, #6b46c1);
        }

        .dark & {
          color: var(--color-primary-light, #9f7aea);
        }
      }
    }
  }


  // Step connector line
  .step-line-container {
    left: 11px;
    top: 30px;
    bottom: -20px;
    width: 2px;
    height: calc(100% - 10px);
    opacity: 1;
    visibility: visible;
    will-change: height;
    transform: translateZ(0);

    .light & {
      background-color: #e9ecef;
    }

    .dark & {
      background-color: #555;
    }

    &.hidden-line {
      opacity: 0;
      visibility: hidden;
    }
  }

  .step-line {
    top: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%);
    transition: height 2.0s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;

    &.completed {
      height: 100%;
    }

    &.animating,
    &.expanding {
      animation: connectLine 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
  }


  // Step circle styles
  .step-circle {
    transition: all 0.3s ease-out;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: scale(1);

    .light & {
      border: none;
      background-color: #f8f9fa;
    }

    .dark & {
      background-color: #333;
      border: none;
    }

    &.completed,
    &.active,
    &.failed {
      background-color: transparent !important;
      border: none;
    }

    &.completed,
    &.failed {
      box-shadow: none;
    }

    &.failed {
      color: white;
      transition: all 0.3s ease-out;

      .stepper-item.in-progress-mode & {
        cursor: pointer !important;
        pointer-events: auto;
      }
    }

    &.processing {
      cursor: pointer !important;
      pointer-events: auto;
    }
  }


  // Step icons
  .step-icon {
    top: 0;
    left: 0;
    animation: fadeInScale 0.3s ease-out;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  }

  // Step number styling
  .step-number {
    .light & {
      color: var(--text-secondary, #4a5568);
    }

    .dark & {
      color: var(--text-secondary-dark, #e2e8f0);
    }
  }

  .active .step-number {
    .light & {
      color: var(--color-primary, #6b46c1);
    }

    .dark & {
      color: var(--color-primary-light, #9f7aea);
    }
  }


  // Step title styling
  .step-title {
    transition: color 0.3s;

    .stepper-item.active:not(.completed) &,
    .stepper-item.in-progress-mode & {
      cursor: pointer !important;
      pointer-events: auto;
    }

    .light & {
      color: var(--text-primary, #2d3748);
    }

    .dark & {
      color: var(--text-primary-dark, #f8f9fa);
    }

    &:hover {
      .light & {
        color: var(--color-primary, #6b46c1);
      }

      .dark & {
        color: var(--color-primary-light, #9f7aea);
      }
    }
  }


  // Retry button styling
  .step-retry-button {
    background-color: #7e3af2;
    color: white;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(126, 58, 242, 0.4);

    .light & {
      background-color: #7e3af2;
      color: white;

      &:hover {
        background-color: #6929c4;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(126, 58, 242, 0.5);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(126, 58, 242, 0.3);
      }
    }

    .dark & {
      background-color: #7e3af2;
      color: white;

      &:hover {
        background-color: #8b5cf6;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.5);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
      }
    }

    img {
      transform: rotate(-10deg);
    }

    &:focus {
      outline: none;
    }

    &:hover img {
      animation: pulse-icon 1s infinite alternate;
    }

    &:active {
      animation: shake-button 0.5s ease-in-out;
    }
  }


  // Step description styling
  .step-description {
    transition:
      max-height 0.5s cubic-bezier(0.25, 0.1, 0.25, 1),
      opacity 0.5s ease-out,
      margin 0.5s ease-out;
    max-height: 1000px;
    opacity: 1;
    will-change: max-height, opacity, margin;
    transform: translateZ(0);

    .light & {
      color: var(--text-secondary, #4a5568);
    }

    .dark & {
      color: var(--text-secondary-dark, #cbd5e0);
    }

    &.collapsed {
      max-height: 0;
      opacity: 0;
      margin-top: 0;
      margin-bottom: 0;
      transition:
        max-height 0.5s cubic-bezier(0.25, 0.1, 0.25, 1),
        opacity 0.5s ease-out,
        margin 0.5s ease-out;
    }

    ::ng-deep {
      p {
        margin: 0 0 8px 0;
      }

      ul,
      ol {
        margin: 8px 0;
        padding-left: 20px;
      }
    }
  }


  // Loading spinner styling
  .modern-loading-spinner {
    top: 0;
    left: 0; 
    .spinner-ring {
      border: 3px solid transparent;
      border-top-color: #6566cd;
      border-bottom-color: #e30a6d;
      filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
      animation: spin-ring 1.5s ease-in-out infinite;
    }

    .spinner-core {
      background: linear-gradient(135deg, #6566cd 0%, #e30a6d 100%);
      box-shadow: 0 0 10px rgba(229, 10, 109, 0.5);
      animation: pulse 1.5s ease-in-out infinite alternate;
    }
  }


  // Next step styling
  .step-next {
    transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .next-title {
    .light & {
      color: var(--text-primary, #2d3748);
    }

    .dark & {
      color: var(--text-primary-dark, #f8f9fa);
    }
  }

  // Restart button container
  .restart-button-container {
    margin-top: 40px;
  }


  // Timer styling
  .step-timer {
    color: var(--code-viewer-text);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    backdrop-filter: blur(10px);
    animation: timerFadeIn 0.3s ease-out;

    .light & {
      background: rgba(0, 0, 0, 0.85);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.15);
    }

    .dark & {
      background: rgba(0, 0, 0, 0.9);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    &.completed {
      opacity: 0.8;

      .light & {
        background: rgba(34, 197, 94, 0.15);
        color: #16a34a;
        border: 1px solid rgba(34, 197, 94, 0.3);
      }

      .dark & {
        background: rgba(34, 197, 94, 0.2);
        color: #4ade80;
        border: 1px solid rgba(34, 197, 94, 0.4);
      }
    }
  }


  // Restart button styling
  .restart-button {
    padding: 12px 24px;
    background-color: #6b46c1;
    color: white;
    font-weight: 600;
    transition: background-color 0.3s;

    &:hover {
      background-color: #553c9a;
    }
  }

  // Loading spinner positioning
  .step-circle.active .modern-loading-spinner {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }

  .step-circle.failed .modern-loading-spinner {
    display: none;
  }


  // Typewriter animations
  .step-title.typing .step-title-text {
    transition: all 0.1s ease-out;
  }

  .step-description.typing ::ng-deep p {
    display: inline;
    transition: all 0.05s ease-out;
  }

  // Shimmer effect for retry
  .step-description.shimmer {
    ::ng-deep p,
    ::ng-deep li,
    ::ng-deep code,
    ::ng-deep pre {
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;

      .light & {
        text-shadow: 0 0 0 var(--text-secondary, #4a5568);
      }

      .dark & {
        text-shadow: 0 0 0 var(--text-secondary-dark, #cbd5e0);
      }
    }
  }

  // Keyframe animations
  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes pulse-icon {
    0% {
      transform: rotate(-10deg) scale(1);
    }
    100% {
      transform: rotate(-10deg) scale(1.2);
    }
  }

  @keyframes shake-button {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(2px);
    }
  }

  @keyframes spin-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes timerFadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes connectLine {
    0% {
      height: 0;
      opacity: 1;
    }
    100% {
      height: 100%;
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
}
