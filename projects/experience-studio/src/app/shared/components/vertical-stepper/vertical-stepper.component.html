<!-- Vertical Stepper Component -->
<div class="vertical-stepper" [ngClass]="theme">
  <div class="stepper-container d-flex flex-column gap-2 align-items-start">
    <div class="stepper-item d-flex position-relative w-100 align-self-start"
         *ngFor="let step of steps; let i = index; let isLast = last"
         [ngClass]="getStepStatus(i)"
         [class.hidden]="!shouldShowStep(i)"
         [class.collapsed]="isStepCollapsed(i)"
         [class.in-progress-mode]="status === 'IN_PROGRESS' && isProcessingStep(i)">

      <!-- Connector Line -->
      <div class="step-line-container position-absolute stepper-z-index-0"
           *ngIf="!isLast"
           [class.hidden-line]="isStepCollapsed(i)">
        <div class="step-line position-absolute"
             [class.completed]="getStepStatus(i) === 'completed'"
             [class.animating]="isLineAnimating(i)"
             [class.expanding]="!isStepCollapsed(i)"></div>
      </div>

      <!-- Step Circle -->
      <div class="step-circle stepper-z-index-10 flex-shrink-0 stepper-w-24 stepper-h-24 rounded-circle d-flex align-items-center justify-content-center stepper-mr-16 position-relative cursor-pointer"
           [class.active]="getStepStatus(i) === 'active' && !step.completed && !isFailureStep(step)"
           [class.failed]="(status === 'FAILED' && (i === currentStepIndex || step.title === 'Build Failed')) || isFailureStep(step) || step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
           [class.processing]="status === 'IN_PROGRESS' && currentStepIndex >= 0 && steps[currentStepIndex] && !steps[currentStepIndex].completed && !isFailureStep(step) && step.title !== this.getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
           (click)="toggleStepCollapse(i)">

        <!-- Checkmark icon for completed steps -->
        <img class="step-icon stepper-w-24 stepper-h-24 position-absolute stepper-z-index-20"
             *ngIf="(getStepStatus(i) === 'completed' || step.completed) && !isFailureStep(step) && step.title !== getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
             src="assets/icons/vertical-stepper/checkmark.svg"
             alt="Completed"
             loading="lazy">

        <!-- Error icon for failed steps -->
        <img class="step-icon stepper-w-24 stepper-h-24 position-absolute stepper-z-index-20"
             *ngIf="isFailureStep(step) || (status === 'FAILED' && i === currentStepIndex) || step.title === getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
             src="assets/icons/vertical-stepper/error.svg"
             alt="Failed"
             loading="lazy">
        <!-- Modern Loading Spinner -->
        <div class="modern-loading-spinner stepper-w-24 stepper-h-24 position-absolute d-flex align-items-center justify-content-center"
             *ngIf="getStepStatus(i) === 'active' && !step.completed && status !== 'FAILED' && !isFailureStep(step) && step.title !== getDisplayTitleForProgress(StepperState.BUILD_FAILED)">
          <div class="spinner-ring position-absolute stepper-w-24 stepper-h-24 rounded-circle"></div>
          <div class="spinner-core stepper-w-12 stepper-h-12 rounded-circle"></div>
        </div>

        <!-- Step Number -->
        <span class="step-number font-weight-600 stepper-fs-16"
              *ngIf="getStepStatus(i) !== 'completed' && getStepStatus(i) !== 'active' && !step.completed">
          {{ i + 1 }}
        </span>
      </div>

      <!-- Step Content -->
      <div class="step-content stepper-flex-1 pt-1">
        <!-- Next step preview -->
        <div class="step-next w-100 stepper-opacity-06 stepper-pointer-events-none"
             *ngIf="getStepStatus(i) === 'next'">
          <div class="next-title stepper-fs-18 font-weight-600 mb-2">
            {{ formatTitle(step.visibleTitle || step.title) }}
          </div>
        </div>

        <!-- Active/Completed step content -->
        <div *ngIf="getStepStatus(i) !== 'next'" class="step-content-inner w-100">
          <!-- Step Title with Timer and Retry Button -->
          <h3 class="step-title stepper-fs-18 font-weight-600 mb-2 cursor-pointer d-flex justify-content-between align-items-center"
              [class.typing]="step.isTitleTyping"
              (click)="toggleStepCollapse(i)">
            <span class="step-title-text">
              {{ formatTitle(step.visibleTitle || step.title) }}
            </span>

            <!-- Timer Display -->
            <div class="step-timer ms-auto stepper-bg-transparent stepper-fs-12 font-weight-500 stepper-whitespace-nowrap flex-shrink-0"
                 [class.completed]="step.completionTime !== undefined && !step.timerActive"
                 *ngIf="(step.timerActive && step.elapsedTime !== undefined) || (step.completionTime !== undefined)">
              {{ formatElapsedTime(step.completionTime || step.elapsedTime || 0) }}
            </div>

            <!-- Retry Button -->
            <button *ngIf="shouldShowRetryButton(step, i)"
                    class="step-retry-button d-flex align-items-center justify-content-center stepper-border-none rounded-circle stepper-p-6 stepper-w-32 stepper-h-32 cursor-pointer stepper-ml-8 position-relative stepper-z-index-20"
                    (click)="onRetryClick(i, $event)"
                    [title]="'Retry attempt ' + (step.retryCount || 0) + '/3'">
              <img src="assets/icons/vertical-stepper/retry.svg"
                   alt="Retry"
                   class="stepper-w-18 stepper-h-18"
                   loading="lazy">
            </button>
          </h3>

          <!-- Step Description -->
          <div class="step-description stepper-fs-14 mb-2 overflow-hidden"
               [class.collapsed]="isStepCollapsed(i)"
               [class.typing]="step.isTyping"
               [class.shimmer]="step.isRetrying">
            <markdown [data]="getSanitizedDescription(step.visibleDescription || step.description)"></markdown>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Restart Button Container -->
  <div class="restart-button-container d-flex justify-content-center"
       *ngIf="restartable">
    <button class="restart-button cursor-pointer stepper-border-none rounded"
            (click)="restartStepper()">
      Restart Process
    </button>
  </div>
</div>
