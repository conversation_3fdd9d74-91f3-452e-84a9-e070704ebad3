import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, inject, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HTMLExportResult, HTMLExportService } from '../../services/html-export.service';
import { ToastService } from '../../services/toast.service';
import { createLogger } from '../../utils/logger';

// Import Prism.js for syntax highlighting
declare const Prism: any;

@Component({
  selector: 'app-html-code-viewer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './html-code-viewer.component.html',
  styleUrls: ['./html-code-viewer.component.scss']
})
export class HtmlCodeViewerComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() htmlResult: HTMLExportResult | null = null;
  @Input() theme: 'light' | 'dark' = 'light';
  
  @Output() close = new EventEmitter<void>();
  @Output() download = new EventEmitter<HTMLExportResult>();
  @Output() copy = new EventEmitter<HTMLExportResult>();

  @ViewChild('codeContainer', { static: false }) codeContainer!: ElementRef<HTMLElement>;

  private readonly destroy$ = new Subject<void>();
  private readonly logger = createLogger('HtmlCodeViewer');
  
  private readonly htmlExportService = inject(HTMLExportService);
  private readonly toastService = inject(ToastService);

  // Component state
  isLoading = false;
  isCopying = false;
  isDownloading = false;
  highlightedCode = '';
  codeStats = {
    lines: 0,
    characters: 0,
    size: ''
  };

  // View options
  showLineNumbers = true;
  wrapLines = false;
  fontSize = 14;
  selectedTab: 'html' | 'css' = 'html';

  ngOnInit(): void {
    this.loadPrismLibrary();
    if (this.htmlResult) {
      this.processHTMLResult();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load Prism.js library for syntax highlighting
   */
  private async loadPrismLibrary(): Promise<void> {
    if (typeof Prism !== 'undefined') {
      return;
    }

    try {
      // Load Prism.js dynamically
      await import('prismjs');
      await import('prismjs/components/prism-markup');
      await import('prismjs/components/prism-css');
      await import('prismjs/components/prism-javascript');
      
      this.logger.info('📝 Prism.js library loaded successfully');
    } catch (error) {
      this.logger.error('📝 Failed to load Prism.js library', error);
    }
  }

  /**
   * Process HTML result and highlight code
   */
  private processHTMLResult(): void {
    if (!this.htmlResult || !this.htmlResult.success || !this.htmlResult.html) {
      return;
    }

    this.isLoading = true;
    
    try {
      const code = this.getCurrentCode();
      this.highlightCode(code);
      this.calculateStats(code);
    } catch (error) {
      this.logger.error('📝 Failed to process HTML result', error);
      this.toastService.error('Failed to process HTML code');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Get current code based on selected tab
   */
  private getCurrentCode(): string {
    if (!this.htmlResult) return '';
    
    switch (this.selectedTab) {
      case 'css':
        return this.htmlResult.css || '/* No CSS available */';
      case 'html':
      default:
        return this.htmlResult.html || '<!-- No HTML available -->';
    }
  }

  /**
   * Highlight code using Prism.js
   */
  private highlightCode(code: string): void {
    if (typeof Prism === 'undefined') {
      this.highlightedCode = this.escapeHtml(code);
      return;
    }

    try {
      const language = this.selectedTab === 'css' ? 'css' : 'markup';
      this.highlightedCode = Prism.highlight(code, Prism.languages[language], language);
    } catch (error) {
      this.logger.warn('📝 Prism highlighting failed, using plain text', error);
      this.highlightedCode = this.escapeHtml(code);
    }
  }

  /**
   * Calculate code statistics
   */
  private calculateStats(code: string): void {
    this.codeStats = {
      lines: code.split('\n').length,
      characters: code.length,
      size: this.formatBytes(new Blob([code]).size)
    };
  }

  /**
   * Handle tab change
   */
  onTabChange(tab: 'html' | 'css'): void {
    if (this.selectedTab === tab) return;
    
    this.selectedTab = tab;
    this.processHTMLResult();
  }

  /**
   * Handle copy to clipboard
   */
  onCopyCode(): void {
    if (!this.htmlResult || this.isCopying) return;

    this.isCopying = true;
    
    this.htmlExportService.copyHTMLToClipboard(this.htmlResult)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.copy.emit(this.htmlResult!);
          }
        },
        complete: () => {
          this.isCopying = false;
        }
      });
  }

  /**
   * Handle download
   */
  onDownload(): void {
    if (!this.htmlResult || this.isDownloading) return;

    this.isDownloading = true;
    
    try {
      this.htmlExportService.downloadHTML(this.htmlResult);
      this.download.emit(this.htmlResult);
    } catch (error) {
      this.logger.error('📝 Download failed', error);
      this.toastService.error('Failed to download HTML file');
    } finally {
      this.isDownloading = false;
    }
  }

  /**
   * Handle close modal
   */
  onClose(): void {
    this.close.emit();
  }

  /**
   * Handle backdrop click
   */
  onBackdropClick(event: MouseEvent): void {
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  /**
   * Handle escape key
   */
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.onClose();
    }
  }

  /**
   * Toggle line numbers
   */
  toggleLineNumbers(): void {
    this.showLineNumbers = !this.showLineNumbers;
  }

  /**
   * Toggle line wrapping
   */
  toggleLineWrap(): void {
    this.wrapLines = !this.wrapLines;
  }

  /**
   * Change font size
   */
  changeFontSize(delta: number): void {
    this.fontSize = Math.max(10, Math.min(24, this.fontSize + delta));
  }

  /**
   * Select all code
   */
  selectAllCode(): void {
    if (this.codeContainer?.nativeElement) {
      const range = document.createRange();
      range.selectNodeContents(this.codeContainer.nativeElement);
      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(range);
    }
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Escape HTML for safe display
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Check if CSS tab should be available
   */
  get hasCSSContent(): boolean {
    return !!(this.htmlResult?.css && this.htmlResult.css.trim().length > 0);
  }

  /**
   * Get current code for display
   */
  get currentCode(): string {
    return this.getCurrentCode();
  }

  /**
   * Check if any operation is in progress
   */
  get isOperationInProgress(): boolean {
    return this.isLoading || this.isCopying || this.isDownloading;
  }
}
