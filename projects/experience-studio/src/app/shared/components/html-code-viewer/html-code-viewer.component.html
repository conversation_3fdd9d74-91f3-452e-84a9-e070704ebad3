<!-- HTML Code Viewer Modal -->
<div class="html-code-viewer-overlay" 
     *ngIf="isVisible"
     [class.theme-dark]="theme === 'dark'"
     (click)="onBackdropClick($event)"
     (keydown)="onKeyDown($event)"
     tabindex="-1">

  <div class="html-code-viewer-modal" (click)="$event.stopPropagation()">
    
    <!-- <PERSON>dal Header -->
    <div class="modal-header">
      <div class="header-left">
        <h2 class="modal-title">
          <svg class="title-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z" 
                  fill="currentColor"/>
          </svg>
          HTML Code Viewer
        </h2>
        
        <!-- Code Stats -->
        <div class="code-stats" *ngIf="!isLoading">
          <span class="stat-item">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <path d="M3 3H21V5H3V3ZM3 7H21V9H3V7ZM3 11H21V13H3V11ZM3 15H21V17H3V15ZM3 19H21V21H3V19Z" 
                    fill="currentColor"/>
            </svg>
            {{ codeStats.lines }} lines
          </span>
          <span class="stat-item">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z" 
                    fill="currentColor"/>
            </svg>
            {{ codeStats.size }}
          </span>
          <span class="stat-item">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" 
                    fill="currentColor"/>
            </svg>
            {{ codeStats.characters }} chars
          </span>
        </div>
      </div>

      <div class="header-right">
        <!-- View Options -->
        <div class="view-options">
          <!-- Font Size Controls -->
          <div class="font-size-controls">
            <button class="option-button" 
                    (click)="changeFontSize(-1)"
                    [disabled]="fontSize <= 10"
                    title="Decrease font size">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M19 13H5V11H19V13Z" fill="currentColor"/>
              </svg>
            </button>
            <span class="font-size-display">{{ fontSize }}px</span>
            <button class="option-button" 
                    (click)="changeFontSize(1)"
                    [disabled]="fontSize >= 24"
                    title="Increase font size">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor"/>
              </svg>
            </button>
          </div>

          <!-- Toggle Options -->
          <button class="option-button" 
                  [class.active]="showLineNumbers"
                  (click)="toggleLineNumbers()"
                  title="Toggle line numbers">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M2 17H4V19H2V17ZM2 7H4V9H2V7ZM6 7H22V9H6V7ZM2 12H4V14H2V12ZM6 12H22V14H6V12ZM6 17H22V19H6V17Z" 
                    fill="currentColor"/>
            </svg>
          </button>

          <button class="option-button" 
                  [class.active]="wrapLines"
                  (click)="toggleLineWrap()"
                  title="Toggle line wrapping">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M4 19H6V20.5C6 20.78 6.22 21 6.5 21H7.5C7.78 21 8 20.78 8 20.5V19H20V17H8V15.5C8 15.22 7.78 15 7.5 15H6.5C6.22 15 6 15.22 6 15.5V17H4V19ZM12 15H20V13H12V15ZM4 11H20V9H4V11ZM4 7H20V5H4V7Z" 
                    fill="currentColor"/>
            </svg>
          </button>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button class="action-button copy-button"
                  [disabled]="!htmlResult || isCopying"
                  (click)="onCopyCode()"
                  title="Copy code to clipboard">
            <div class="button-spinner" *ngIf="isCopying">
              <div class="spinner"></div>
            </div>
            <svg class="button-icon" *ngIf="!isCopying" width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" 
                    fill="currentColor"/>
            </svg>
            <span *ngIf="!isCopying">Copy</span>
            <span *ngIf="isCopying">Copying...</span>
          </button>

          <button class="action-button download-button"
                  [disabled]="!htmlResult || isDownloading"
                  (click)="onDownload()"
                  title="Download HTML file">
            <div class="button-spinner" *ngIf="isDownloading">
              <div class="spinner"></div>
            </div>
            <svg class="button-icon" *ngIf="!isDownloading" width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20ZM8 15.01L9.41 16.42L11 14.84V19H13V14.84L14.59 16.43L16 15.01L12.01 11L8 15.01Z" 
                    fill="currentColor"/>
            </svg>
            <span *ngIf="!isDownloading">Download</span>
            <span *ngIf="isDownloading">Downloading...</span>
          </button>
        </div>

        <!-- Close Button -->
        <button class="close-button" 
                (click)="onClose()"
                title="Close viewer">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" 
                  fill="currentColor"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation" *ngIf="hasCSSContent">
      <button class="tab-button"
              [class.active]="selectedTab === 'html'"
              (click)="onTabChange('html')">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z" 
                fill="currentColor"/>
        </svg>
        HTML
      </button>
      <button class="tab-button"
              [class.active]="selectedTab === 'css'"
              (click)="onTabChange('css')">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M5 3L4.35 6.34H17.94L17.5 8.5H3.92L3.26 11.83H16.85L16.09 15.64L10.61 17.45L5.86 15.64L6.19 14H2.85L2.06 18L9.91 21L18.96 18L20.16 11.97L20.4 10.76L21.94 3H5Z" 
                fill="currentColor"/>
        </svg>
        CSS
      </button>
    </div>

    <!-- Code Content -->
    <div class="code-content">
      <!-- Loading State -->
      <div class="loading-state" *ngIf="isLoading">
        <div class="loading-spinner"></div>
        <span>Processing code...</span>
      </div>

      <!-- Code Display -->
      <div class="code-display" 
           *ngIf="!isLoading"
           [class.show-line-numbers]="showLineNumbers"
           [class.wrap-lines]="wrapLines"
           [style.font-size.px]="fontSize">
        
        <pre class="code-pre"><code class="code-block" 
                                   #codeContainer
                                   [innerHTML]="highlightedCode"></code></pre>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="!isLoading && !currentCode">
        <svg class="empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none">
          <path d="M14.6 16.6L19.2 12L14.6 7.4L16 6L22 12L16 18L14.6 16.6ZM9.4 16.6L4.8 12L9.4 7.4L8 6L2 12L8 18L9.4 16.6Z" 
                fill="currentColor"/>
        </svg>
        <h3>No Code Available</h3>
        <p>The selected content doesn't contain any {{ selectedTab.toUpperCase() }} code to display.</p>
      </div>
    </div>

    <!-- Footer -->
    <div class="modal-footer">
      <div class="footer-left">
        <button class="footer-button" 
                (click)="selectAllCode()"
                [disabled]="!currentCode"
                title="Select all code">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
            <path d="M3 5H21V7H3V5ZM3 11H21V13H3V11ZM3 17H21V19H3V17Z" fill="currentColor"/>
          </svg>
          Select All
        </button>
      </div>

      <div class="footer-right">
        <span class="footer-info">
          Press <kbd>Esc</kbd> to close
        </span>
      </div>
    </div>
  </div>
</div>
