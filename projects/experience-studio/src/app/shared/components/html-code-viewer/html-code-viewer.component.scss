@import '../../../assets/styles/variables';
@import '../../../assets/styles/mixins';

.html-code-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: overlayFadeIn 0.3s ease;

  &.theme-dark {
    --surface-color: #1f2937;
    --surface-secondary: #111827;
    --border-color: #374151;
    --text-color: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --button-bg: #374151;
    --button-hover: #4b5563;
    --button-active: #6b7280;
    --code-bg: #0f172a;
    --code-border: #1e293b;
    --line-number-bg: #1e293b;
    --line-number-color: #64748b;
  }

  @keyframes overlayFadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(4px);
    }
  }
}

.html-code-viewer-modal {
  background: var(--surface-color, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-2rem) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

// Modal Header
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-color, #ffffff);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #111827);

  .title-icon {
    color: var(--text-secondary, #6b7280);
  }
}

.code-stats {
  display: flex;
  align-items: center;
  gap: 1rem;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-muted, #6b7280);

    svg {
      opacity: 0.7;
    }
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

// View Options
.view-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.font-size-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem;
  background: var(--surface-secondary, #f9fafb);
  border-radius: 0.375rem;
  border: 1px solid var(--border-color, #e5e7eb);
}

.font-size-display {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
  min-width: 2rem;
  text-align: center;
}

.option-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--button-hover, #f3f4f6);
    color: var(--text-color, #111827);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.active {
    background: var(--button-active, #e5e7eb);
    color: var(--text-color, #111827);
    border-color: var(--border-color, #d1d5db);
  }
}

// Action Buttons
.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--button-bg, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.375rem;
  color: var(--text-color, #374151);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--button-hover, #f3f4f6);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  .button-icon {
    flex-shrink: 0;
  }

  &.copy-button {
    background: #eff6ff;
    border-color: #93c5fd;
    color: #1e40af;

    &:hover:not(:disabled) {
      background: #dbeafe;
    }
  }

  &.download-button {
    background: #f0fdf4;
    border-color: #86efac;
    color: #14532d;

    &:hover:not(:disabled) {
      background: #dcfce7;
    }
  }
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.375rem;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #fee2e2;
    border-color: #fca5a5;
    color: #dc2626;
  }
}

// Tab Navigation
.tab-navigation {
  display: flex;
  background: var(--surface-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  flex-shrink: 0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-secondary, #6b7280);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: var(--text-color, #111827);
    background: var(--surface-color, #ffffff);
  }

  &.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: var(--surface-color, #ffffff);
  }
}

// Code Content
.code-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
  color: var(--text-secondary, #6b7280);

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-color, #e5e7eb);
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.code-display {
  flex: 1;
  overflow: auto;
  background: var(--code-bg, #f8fafc);
  border: 1px solid var(--code-border, #e2e8f0);

  &.show-line-numbers {
    .code-pre {
      counter-reset: line;
      
      .code-block {
        padding-left: 3.5rem;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3rem;
          background: var(--line-number-bg, #f1f5f9);
          border-right: 1px solid var(--border-color, #e2e8f0);
        }
      }
    }
  }

  &.wrap-lines {
    .code-block {
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

.code-pre {
  margin: 0;
  height: 100%;
}

.code-block {
  display: block;
  padding: 1rem;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color, #1e293b);
  background: transparent;
  white-space: pre;
  overflow-x: auto;
  height: 100%;
  box-sizing: border-box;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
  color: var(--text-secondary, #6b7280);
  text-align: center;

  .empty-icon {
    opacity: 0.5;
  }

  h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
    max-width: 24rem;
  }
}

// Modal Footer
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-secondary, #f9fafb);
  flex-shrink: 0;
}

.footer-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.375rem;
  color: var(--text-secondary, #6b7280);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--button-hover, #f3f4f6);
    color: var(--text-color, #111827);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.footer-info {
  font-size: 0.75rem;
  color: var(--text-muted, #9ca3af);

  kbd {
    padding: 0.125rem 0.25rem;
    background: var(--surface-color, #ffffff);
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 0.25rem;
    font-family: inherit;
    font-size: 0.75rem;
  }
}

// Loading Spinner
.button-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;

  .spinner {
    width: 0.875rem;
    height: 0.875rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .html-code-viewer-modal {
    width: 95vw;
    height: 90vh;
  }

  .modal-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem;
  }

  .header-left,
  .header-right {
    justify-content: space-between;
  }

  .code-stats {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .view-options,
  .action-buttons {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .action-button span {
    display: none;
  }

  .code-display.show-line-numbers .code-block {
    padding-left: 2.5rem;

    &::before {
      width: 2rem;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .html-code-viewer-modal {
    border: 2px solid var(--border-color, #000000);
  }

  .option-button,
  .action-button,
  .close-button,
  .tab-button,
  .footer-button {
    border-width: 2px;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .html-code-viewer-overlay,
  .html-code-viewer-modal,
  .loading-spinner,
  .button-spinner .spinner {
    animation: none;
  }

  .action-button:hover:not(:disabled) {
    transform: none;
  }
}
