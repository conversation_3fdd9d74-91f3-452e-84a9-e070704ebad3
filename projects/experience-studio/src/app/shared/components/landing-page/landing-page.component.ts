import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject
} from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HeroSectionHeaderComponent } from '../hero-section-header/hero-section-header.component';
import { RecentCreationComponent } from '../recent-creation/recent-creation.component';
import { CardDataService } from '../../services/data-services/card-data.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { ToastService } from '../../services/toast.service';
import { CardSelectionService } from '../../services/card-selection.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';
import { createThemeManager, THEME_COLORS, getThemeColor } from '../../utils/theme-manager.util';

interface StudioCard {
  readonly id: number;
  readonly title: string;
  readonly description: string;
  readonly image: string;
  readonly path: string;
  readonly type: string;
  readonly disabled?: boolean;
}

type Theme = 'light' | 'dark';

const STUDIO_CARDS: readonly StudioCard[] = [
  {
    id: 1,
    title: 'Generate UI Design',
    description: 'Create and generate UI designs with AI. Transform your ideas into beautiful interfaces.',
    image: '/assets/cards-images/ui_design.svg',
    path: 'prompt',
    type: 'generate-ui-design',
    disabled: false
  },
  {
    id: 2,
    title: 'Generate Application',
    description: 'Create complete applications with AI. From concept to functional code in minutes.',
    image: '/assets/cards-images/app_generation.svg',
    path: 'prompt',
    type: 'image-to-code'
  }
] as const;

@Component({
  selector: 'app-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    HeroSectionHeaderComponent,
    RecentCreationComponent,
  ],
  templateUrl: './landing-page.component.html',
  styleUrl: './landing-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LandingPageComponent implements OnInit {
  private readonly router = inject(Router);
  private readonly cardDataService = inject(CardDataService);
  private readonly toastService = inject(ToastService);
  private readonly cardSelectionService = inject(CardSelectionService);

  // Modern theme management using functional approach
  private readonly themeManager = createThemeManager();

  readonly studioCards = STUDIO_CARDS;

  // Theme-reactive computed styles using the new theme manager
  get theme(): Theme { return this.themeManager.theme(); }
  get cardTextColor(): string { return getThemeColor('text', this.theme); }
  get cardDescriptionColor(): string { return getThemeColor('description', this.theme); }
  get cardBorderColor(): string { return getThemeColor('border', this.theme); }
  get cardBackground(): string { return 'transparent'; } // Consistent transparent background

  readonly trackByCardId = (_: number, card: StudioCard): number => card.id;

  /**
   * Get theme-aware divider image
   */
  getDividerImage(): string {
    return this.themeManager.getThemeAsset('/assets/icons/divider', 'svg');
  }

  // Route mappings for O(1) lookup performance
  private readonly routeMap = new Map<string, string>([
    ['Generate UI Design', '/experience/generate-ui-design'],
    ['Generate Application', '/experience/generate-application']
  ]);

  private readonly actionMap = new Map<string, string>([
    ['Generate UI Design', 'UI'],
    ['Generate Application', 'Application']
  ]);

  ngOnInit(): void {
    // Theme management is now handled automatically by themeManager
    this.cardSelectionService.resetSelectionState();
  }

  navigateToStudio(card: StudioCard, event: Event): void {
    event.stopPropagation();

    if (card.disabled) {
      this.toastService.info('This feature is in build mode.');
      return;
    }

    this.cardDataService.setSelectedCardTitle(card.title);
    this.cardSelectionService.setCardSelected(true);

    const routePrefix = this.getRoutePrefix(card.title);
    const actionType = this.getActionType(card.title);

    this.toastService.info(`Starting ${actionType} generation`);
    this.router.navigate([`${routePrefix}/prompt`]);
  }

  private getRoutePrefix(title: string): string {
    return this.routeMap.get(title) || '/experience/generate-application';
  }

  private getActionType(title: string): string {
    return this.actionMap.get(title) || 'Application';
  }
}
