// CSS Variables
:root {
  --primary-rgb: 168, 169, 227; // Light purple color in RGB format

  // Canvas dot grid variables (Light theme)
  --canvas-bg: #ffffff;
  --canvas-dot-color: rgba(0, 0, 0, 0.12);
  --canvas-dot-size: 1px;
  --canvas-grid-size: 20px;

  // UI Design node variables (Light theme)
  --node-bg: #ffffff;
  --node-border: #e5e7eb;
  --node-hover-border: #3b82f6;
  --node-selected-border: #3b82f6;
  --node-selected-editing-border: #2563eb; // Strong blue for better visibility in light mode
  --node-selected-editing-shadow: rgba(37, 99, 235, 0.4);
  --node-header-bg: #f8fafc;
  --node-header-border: #e5e7eb;
  --node-header-color: #1f2937;

  // Canvas controls variables (Light theme)
  --canvas-controls-bg: rgba(255, 255, 255, 0.95);
  --canvas-controls-border: #e1e5e9;
  --canvas-btn-border: #d1d5db;
  --canvas-btn-color: #374151;
  --canvas-btn-hover-bg: #f3f4f6;
  --canvas-btn-hover-border: #9ca3af;
  --canvas-zoom-color: #6b7280;
  --canvas-icon-color: #1f2937;

  // Modal icon colors (Light theme)
  --modal-icon-color: #374151;

  // Canvas tooltip variables (Light theme)
  --tooltip-bg: rgba(255, 255, 255, 0.95);
  --tooltip-border: #e1e5e9;
  --tooltip-text: #374151;
  --tooltip-icon: #6b7280;

  // Design card colors (Light theme) - Reference design match
  --design-card-bg: #ffffff;
  --design-card-border: rgba(0, 0, 0, 0.08);
  --design-card-border-hover: rgba(59, 130, 246, 0.3);
  --design-card-border-selected: rgba(59, 130, 246, 0.5);
  --design-card-title-bg: #ffffff;
  --design-card-title-text: #1f2937;
  --design-card-content-bg: #f8fafc;

  // Design card shadows (Light theme) - Clean and subtle
  --design-card-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  --design-card-shadow-hover:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1);
  --design-card-shadow-selected:
    0 4px 12px rgba(59, 130, 246, 0.25),
    0 2px 6px rgba(59, 130, 246, 0.15),
    0 0 0 2px rgba(59, 130, 246, 0.2);

  // Multi-selection variables (Light theme)
  --multi-select-overlay-bg: rgba(59, 130, 246, 0.1);
  --multi-select-border: rgba(59, 130, 246, 0.6);
  --multi-select-label-bg: rgba(59, 130, 246, 0.9);
  --multi-select-label-text: #ffffff;
  --selection-controls-bg: rgba(255, 255, 255, 0.95);
  --selection-controls-border: #e1e5e9;
  --selection-btn-bg: #f3f4f6;
  --selection-btn-hover-bg: #e5e7eb;
  --selection-btn-disabled-bg: #f9fafb;
  --selection-btn-disabled-color: #9ca3af;
  --selection-info-bg: rgba(59, 130, 246, 0.05);
  --selection-info-color: #3b82f6;

  // Node title highlighting (Light theme) - Enhanced visibility
  --node-title-selected-color: #ffffff; // White text for better contrast
  --node-title-selected-bg: #2563eb; // Strong blue background
  --node-title-selected-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  --node-title-focus-outline: 2px solid #3b82f6;
}

// Dark theme canvas variables
:host-context(.dark-theme) {
  --canvas-bg: #1a1a1a;
  --canvas-dot-color: rgba(255, 255, 255, 0.10);
  --canvas-dot-size: 1px;
  --canvas-grid-size: 20px;

  // UI Design node variables (Dark theme)
  --node-bg: #2a2a2a;
  --node-border: #404040;
  --node-hover-border: #3b82f6;
  --node-selected-border: #3b82f6;
  --node-selected-editing-border: #60a5fa; // Bright blue for dark theme visibility
  --node-selected-editing-shadow: rgba(96, 165, 250, 0.5);
  --node-header-bg: #1f1f1f;
  --node-header-border: #404040;
  --node-header-color: #e5e7eb;

  // Canvas controls variables (Dark theme)
  --canvas-controls-bg: rgba(42, 42, 42, 0.95);
  --canvas-controls-border: #404040;
  --canvas-btn-border: #525252;
  --canvas-btn-color: #d1d5db;
  --canvas-btn-hover-bg: #374151;
  --canvas-btn-hover-border: #6b7280;
  --canvas-zoom-color: #9ca3af;
  --canvas-icon-color: #d1d5db;

  // Modal icon colors (Dark theme)
  --modal-icon-color: #d1d5db;

  // Canvas tooltip variables (Dark theme)
  --tooltip-bg: rgba(42, 42, 42, 0.95);
  --tooltip-border: #404040;
  --tooltip-text: #e5e7eb;
  --tooltip-icon: #9ca3af;

  // Design card colors (Dark theme) - Enhanced visibility
  --design-card-bg: #1f2937;
  --design-card-border: rgba(255, 255, 255, 0.1);
  --design-card-border-hover: rgba(96, 165, 250, 0.4);
  --design-card-border-selected: rgba(96, 165, 250, 0.6);
  --design-card-title-bg: #1f2937;
  --design-card-title-text: #f9fafb;
  --design-card-content-bg: #111827;

  // Design card shadows (Dark theme) - Enhanced depth
  --design-card-shadow:
    0 2px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2);
  --design-card-shadow-hover:
    0 6px 16px rgba(0, 0, 0, 0.4),
    0 3px 8px rgba(0, 0, 0, 0.25);
  --design-card-shadow-selected:
    0 6px 16px rgba(96, 165, 250, 0.3),
    0 3px 8px rgba(96, 165, 250, 0.2),
    0 0 0 2px rgba(96, 165, 250, 0.3);

  // Multi-selection variables (Dark theme)
  --multi-select-overlay-bg: rgba(59, 130, 246, 0.15);
  --multi-select-border: rgba(59, 130, 246, 0.8);
  --multi-select-label-bg: rgba(59, 130, 246, 0.9);
  --multi-select-label-text: #ffffff;
  --selection-controls-bg: rgba(42, 42, 42, 0.95);
  --selection-controls-border: #404040;
  --selection-btn-bg: #374151;
  --selection-btn-hover-bg: #4b5563;
  --selection-btn-disabled-bg: #1f2937;
  --selection-btn-disabled-color: #6b7280;
  --selection-info-bg: rgba(59, 130, 246, 0.1);
  --selection-info-color: #93c5fd;

  // Node title highlighting (Dark theme) - Enhanced visibility
  --node-title-selected-color: #1e293b; // Dark text for contrast
  --node-title-selected-bg: #60a5fa; // Bright blue background
  --node-title-selected-shadow: 0 2px 8px rgba(96, 165, 250, 0.4);
  --node-title-focus-outline: 2px solid #60a5fa;
}

// Animation keyframes
@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Smooth split screen styles with maximum hardware acceleration
.smooth-split-screen {
  // Apply hardware acceleration to the container itself
  transform: translateZ(0) scale(1); // Force hardware acceleration
  backface-visibility: hidden; // Prevent flickering
  perspective: 1000px; // Enhance 3D rendering
  will-change: transform; // Optimize for animations
  contain: layout paint style; // Contain repaints for better performance
  -webkit-font-smoothing: antialiased; // Smoother text rendering
  -moz-osx-font-smoothing: grayscale; // Smoother text rendering in Firefox

  // Prevent text selection during resize
  &.resizing * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: none !important;
  }

  // Maximum hardware acceleration for panels
  .awe-leftpanel,
  .awe-rightpanel {
    transform: translateZ(0) scale(1); // Force hardware acceleration
    backface-visibility: hidden; // Prevent flickering
    perspective: 1000px; // Enhance 3D rendering
    will-change: width, transform; // Optimize for animations
    contain: layout paint style; // Contain repaints for better performance
    -webkit-font-smoothing: antialiased; // Smoother text rendering
    -moz-osx-font-smoothing: grayscale; // Smoother text rendering in Firefox
    transition: width 0.08s cubic-bezier(0.4, 0, 0.2, 1); // Faster Material Design easing

    // Force hardware acceleration for all child elements
    * {
      transform: translateZ(0);
      backface-visibility: hidden;
      will-change: transform;
      max-width: 100%;
    }
  }

  // Ensure the resizer is ultra-smooth
  .resizer {
    transform: translateZ(0);
    will-change: transform;
    contain: layout paint style;
  }
}

// Combined animations
@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Layout
.container {
  width: 100%;
  // height: calc(100vh - 56px);
  position: relative;
  display: flex;
  flex-direction: column;
}

// Custom Modal Styles
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-modal-content {
  background-color: #f5f5f5;
  border-radius: 16px;
  width: 500px;
  max-width: 90vw;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  animation: slideIn 0.3s ease-out;
  overflow: hidden;
  z-index: 1001;
}

.custom-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #eaeaea;
}

.custom-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.custom-close-button {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.custom-modal-body {
  padding: 0 20px;
}

// Dark theme support
:host-context(.dark-theme) {
  .custom-modal-content {
    background-color: #2a2a2a;
  }

  .custom-modal-header {
    border-bottom-color: #444;
  }

  .custom-modal-header h3 {
    color: #e0e0e0;
  }

  .custom-close-button {
    color: #aaa;
  }

  .custom-close-button:hover {
    color: #fff;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Export modal styles
.sharable-link-section {
  margin-bottom: 30px;

  .section-label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #333;
  }

  .link-input-container {
    display: flex;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;

    .link-input {
      flex: 1;
      padding: 12px 16px;
      border: 1px solid #e0e0e0;
      border-right: none;
      border-radius: 8px 0 0 8px;
      font-size: 14px;
      color: #333;
      background-color: #f5f5f5;
    }

    .copy-button {
      padding: 12px 24px;
      background-color: #e94caf;
      color: white;
      border: none;
      border-radius: 0 8px 8px 0;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        background-color: #d43c9a;
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(233, 76, 175, 0.2);
      }

    }


  }
}

.export-options {
  .export-option-row {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;

    .export-option-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      &:hover {
        .option-icon-container {
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }

        .option-label {
          font-weight: 500;
        }
      }

      &:active {
        .option-icon-container {
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }

      .option-icon-container {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.2s ease;
      }

      .option-label {
        font-size: 14px;
        color: #333;
        text-align: center;
        transition: font-weight 0.2s ease;
      }
    }
  }
}

// Dark theme support for export modal
:host-context(.dark-theme) {
  .sharable-link-section {
    .section-label {
      color: #e0e0e0;
    }

    .link-input-container {
      .link-input {
        background-color: #2a2a2a;
        border-color: #444;
        color: #e0e0e0;
      }
    }
  }

  .export-options {
    .export-option-row {
      .export-option-item {
        &:hover {
          .option-label {
            color: #ffffff;
          }
        }

        .option-icon-container {
          background-color: #2a2a2a;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

          // Apply filter to icons in dark mode
          .dark-icon {
            filter: invert(1) brightness(1.5);
          }
        }

        &:hover .option-icon-container {
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
        }

        .option-label {
          color: #e0e0e0;
          transition: color 0.2s ease, font-weight 0.2s ease;
        }
      }
    }
  }
}

// Header styles
.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  min-height:56px !important;
  box-sizing: border-box;
  transition: all 0.3s ease;
  border-radius: 4px 4px 0 0;
  border: 1px solid var(--awe-split-border-color);
  background: var(--code-viewer-bg);
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;

  // Dark theme specific styles
  &.dark-theme {
    background: var(--code-viewer-bg);
    border-color: var(--awe-split-border-color);

    .header-center {
      .project-name {
        color: var(--body-text-color-dark, #f0f0f0);
        border: 1px solid transparent;

        &.shimmer {
          background: linear-gradient(90deg,
            rgba(255, 255, 255, 0.05) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(255, 255, 255, 0.05) 100%);
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    awe-icons {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .export-icon {
      margin-right: 8px;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }

    // Tabs container styles
    .tabs-container {
      display: flex;
      align-items: center;
      gap: 24px;

      border-radius: 6px;
      // padding: 2px;
    }
  }

  .header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;

    .project-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--body-text-color, #333333);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 250px;
      text-align: center;
      padding: 4px 8px;
      letter-spacing: 0.2px;
      transition: opacity 0.3s ease,transform 0.3s ease;

      &.shimmer {
        background: linear-gradient(90deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0.1) 100%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 4px;
        min-width: 200px;
        min-height: 22px;
      }

      &.hidden {
        opacity: 0;
        transform: scale(0.8);
        pointer-events: none;
        transition: opacity 0.3s ease, transform 0.3s ease;
      }
    }
  }
}

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .icon-group {
      display: flex;
      align-items: center;
      gap: 12px;

      awe-icons {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          opacity: 0.8;
        }

        &.cursor-pointer {
          cursor: pointer;
        }
      }
    }

    .edit-element-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 4px;
      background: var(--edit-btn-bg, rgba(0, 0, 0, 0.1));
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 14px;

      &:hover {
        background: var(--edit-btn-hover-bg, rgba(0, 0, 0, 0.2));
      }
    }
  }



// Button styles
.custom-button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  position: relative;

  &:hover:not(.disabled):not(.code-generation-pending) {
    background: var(--hover-bg, rgba(0, 0, 0, 0.05));
  }

  &.active {
    background: var(--active-bg, rgba(0, 0, 0, 0.1));
    color: var(--active-text);
    font-weight: 500;
  }

  // Error tab styling
  &.error-tab {
    &.active {
      background: rgba(220, 53, 69, 0.15);
      color: #dc3545;
      font-weight: 500;
    }

    &:hover:not(.disabled):not(.code-generation-pending) {
      background: rgba(220, 53, 69, 0.1);
    }
  }

  // Dark theme specific active state
  .dark-theme & {
    &:hover:not(.disabled):not(.code-generation-pending) {
      background: var(--hover-bg, rgba(255, 255, 255, 0.1));
    }

    &.active {
      background: var(--active-bg, rgba(255, 255, 255, 0.15));
      color: var(--active-text, #ffffff);
      font-weight: 500;
    }

    // Error tab styling for dark theme
    &.error-tab {
      &.active {
        background: rgba(220, 53, 69, 0.25);
        color: #ff6b6b;
        font-weight: 500;
      }

      &:hover:not(.disabled):not(.code-generation-pending) {
        background: rgba(220, 53, 69, 0.2);
      }
    }
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    background-color: var(--button-disabled-bg, rgba(0, 0, 0, 0.05));
    color: var(--button-disabled-text, #999);
    border-color: var(--button-disabled-border, transparent);
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--button-disabled-bg, rgba(0, 0, 0, 0.05));
    }
  }

  // Enhanced Preview Tab States
  &.preview-tab {
    position: relative;

    .tab-text {
      display: inline-block;
    }

    .tab-status {
      margin-left: 6px;
      font-size: 12px;
    }

    .preview-loading-spinner {
      color: var(--awe-primary-color, #007bff);
      animation: spin 1s linear infinite;
    }

    .error-indicator {
      color: var(--awe-error-color, #dc3545);
    }

    // Loading state
    &.loading {
      background-color: var(--awe-loading-bg, rgba(0, 123, 255, 0.1));
      border-color: var(--awe-loading-border, rgba(0, 123, 255, 0.3));
      cursor: wait;

      .tab-text {
        opacity: 0.8;
      }
    }

    // Enabled state (ready for interaction)
    // &.enabled {
    //   // background-color: var(--awe-success-bg, rgba(40, 167, 69, 0.1));
    //   // border-color: var(--awe-success-border, rgba(40, 167, 69, 0.3));
    // }

    // Error state
    &.error-tab {
      background-color: var(--awe-error-bg, rgba(220, 53, 69, 0.1));
      border-color: var(--awe-error-border, rgba(220, 53, 69, 0.3));

      &:hover {
        background-color: var(--awe-error-hover-bg, rgba(220, 53, 69, 0.2));
        border-color: var(--awe-error-hover-border, rgba(220, 53, 69, 0.5));
      }
    }

    // Enhanced disabled state for preview tab
    &.disabled {
      background-color: var(--awe-disabled-bg, rgba(108, 117, 125, 0.1));
      border-color: var(--awe-disabled-border, rgba(108, 117, 125, 0.2));
      color: var(--awe-disabled-text, #6c757d);
      cursor: not-allowed;
      pointer-events: none;
      opacity: 0.7;

      .tab-text {
        opacity: 0.6;
      }
    }
  }

  // Dark theme specific disabled state
  .dark-theme & {
    &.disabled {
      background-color: var(--button-disabled-bg, rgba(255, 255, 255, 0.05));
      color: var(--button-disabled-text, rgba(255, 255, 255, 0.4));
      border-color: var(--button-disabled-border, transparent);

      &:hover {
        background-color: var(--button-disabled-bg, rgba(255, 255, 255, 0.05));
      }
    }
  }

  &.generating {
    position: relative;

    &::after {
      content: 'Generating...';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      font-size: 10px;
      white-space: nowrap;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }

  // Tab status indicator (hourglass icon)
  .tab-status {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 10px;
    animation: pulse 1.5s infinite ease-in-out;

    &.spinning {
      animation: spin 1s infinite linear;
      color: #0288d1; // Blue color for streaming indicator
    }
  }
}

// Animation keyframes already defined at the top

// Content area styles
.border {
  border: 1px solid var(--awe-split-border-color);
  border-radius: 4px;
  height: 100%;
}

// History styles
.history-container {
  height: 80vh;
  overflow-y: auto;
  padding: 5px;
  background-color: var(--chat-window-card-bg);

  .history-content {
    padding: 16px;

    .history-header {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;

      awe-icons {
        margin-right: 1rem;
        cursor: pointer;
        font-size: 1.25rem;
        color: var(--chat-window-icon-color);

        &:hover {
          color: var(--primary-color);
        }
      }

      h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--chat-window-text-color);
      }
    }

    .history-cards-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.5rem;

      // Ensure cards take full width on small screens
      @media (max-width: 576px) {
        grid-template-columns: 1fr;
      }

      // Add some spacing between cards
      awe-experience-history-cards {
        margin-bottom: 0.5rem;
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-5px);
        }
      }
    }
  }
}

// Code generation message
.code-generation-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: var(--background-color, #ffffff);
  color: var(--body-text-color, #333333);
  text-align: center;
  padding: 2rem;

  .message-content {
    max-width: 500px;

    i {
      font-size: 3rem;
      display: block;
      margin-bottom: 1rem;
      animation: pulse 2s infinite;

      &.spinning-icon {
        animation: spin 1.5s infinite linear;
        color: #0288d1; // Blue color for spinning icon
      }
    }

    .status-message {
      margin-top: 1rem;
      font-style: italic;
      color: #0288d1; // Blue color for status message
    }

    h4 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      font-weight: 500;
    }

    p {
      font-size: 1rem;
      color: var(--text-secondary, #6c757d);
    }
  }
}

// Preview styles
.preview-view {
  height: 80vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--awe-split-border-color);
  border-radius: 0 0 8px 8px;
}

// Preview controls
.preview-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.preview-fullscreen-btn {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }

  i {
    font-size: 16px;
  }
}

// Preview iframe styles
.preview-frame {
  width: 100%;
  height: 80vh;
  border: none;
  background-color: white;
  display: block;
}
.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-in-out;
  backdrop-filter: blur(10px);
  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
    background: transparent;
    border: none;
    color: white;
    font-size: 32px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
  img {
    max-width: 90vw;
    max-height: 90vh;
    object-fit: contain;
    animation: fadeIn 0.3s ease-in-out;
  }
}
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

// Layout examples container styles
.layout-examples-container {
  height: 80vh;
  width: 100%;
  // min-height: calc(100vh - 120px); // Match other containers
  padding: 20px;
  background-color: transparent; /* Changed to transparent */
  color: var(--body-text-color, #333333);
  overflow-y: auto;
  position: relative; /* Added for absolute positioning of children */

  // Skeleton UI styles
  .skeleton-card {
    background-color: var(--card-bg, #ffffff);
  }

  .skeleton-image {
    height: 180px;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  .skeleton-title {
    height: 24px;
    margin: 10px;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  .skeleton-description {
    height: 16px;
    margin: 0 10px 15px;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  .debug-info {
    padding: 10px;
    margin-top: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    font-size: 12px;
    color: #333;

    p {
      margin: 5px 0;
      word-break: break-all;
    }
  }

  .layout-examples-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;

    h3 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--body-text-color, #333333);
    }

    p {
      font-size: 14px;
      color: var(--text-secondary, #6c757d);
      line-height: 1.5;
    }

    .debug-panel {
      margin-top: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;

      p {
        margin: 5px 0;
        word-break: break-all;
      }

      .debug-button {
        margin-top: 10px;
        padding: 5px 10px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background-color: #0069d9;
        }
      }
    }
  }

  .layout-examples-grid {
    display: grid;
    gap: 20px;
    padding: 15px;

    // Dynamic grid based on number of layouts
    &.layout-count-1 {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &.layout-count-2 {
      grid-template-columns: repeat(2, 1fr);

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    &.layout-count-3 {
      grid-template-columns: repeat(3, 1fr);

      @media (max-width: 992px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    &.layout-count-4 {
      grid-template-columns: repeat(2, 1fr);

      @media (min-width: 992px) {
        grid-template-columns: repeat(4, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    // Default for 5+ layouts
    &:not(.layout-count-1):not(.layout-count-2):not(.layout-count-3):not(.layout-count-4) {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    /* Fullscreen layout grid for the layout identified view */
    &.layout-examples-grid-fullscreen {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 80px;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: calc(100% - 80px);
      padding: 0;
      background-color: transparent;
    }

    .layout-example-item {
      // Full width item for single layout
      &.full-width {
        grid-column: 1 / -1; // Span all columns

        .layout-example-card {
          max-width: 600px;
          margin: 0 auto;
        }
      }

      /* Fullscreen layout item for the layout identified view */
      &.layout-example-item-fullscreen {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .layout-example-card {
        // border: 1px solid var(--border-color, #e0e0e0);
        // border-radius: 8px;
        overflow: hidden;
        transition:
          transform 0.2s ease,
          box-shadow 0.2s ease;
        background-color: transparent; /* Changed to transparent */
        width: 100%;
        height: 100%;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .layout-example-image {
          height: 180px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: transparent; /* Changed to transparent */

          /* Fullscreen layout image for the layout identified view */
          &.layout-example-image-fullscreen {
            height: 100%;
            width: 100%;

            img {
              object-fit: contain;
              max-width: 100%;
              width: auto;
              height: auto;
            }
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.05);
            }

            &.full-screen-image {
              object-fit: cover;
              width: 100%;
            }
          }

          .image-debug-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px;
            font-size: 10px;
            word-break: break-all;

            p {
              margin: 2px 0;
            }
          }
        }

        .layout-example-title {
          padding: 10px;
          text-align: center;
          font-weight: 500;
          font-size: 16px;
          color: var(--body-text-color, #333333);
        }

        .layout-example-description {
          padding: 0 10px 15px;
          text-align: center;
          font-size: 14px;
          color: var(--text-secondary, #6c757d);
        }
      }
    }
  }

  // Dark theme support
  .dark-theme & {
    background-color: transparent; /* Changed to transparent */

    .layout-examples-header {
      h3 {
        color: var(--body-text-color, #e0e0e0);
      }

      p {
        color: var(--text-secondary, #a0a0a0);
      }
    }

    .layout-example-card {
      background-color: transparent; /* Changed to transparent */
      border-color: var(--border-color, #333333);

      .layout-example-image {
        background-color: transparent; /* Changed to transparent */
      }

      .layout-example-title {
        color: var(--body-text-color, #e0e0e0);
      }

      .layout-example-description {
        color: var(--text-secondary, #a0a0a0);
      }
    }

    // Dark theme skeleton UI
    .skeleton-card {
      background-color: var(--card-bg, #1e1e1e);
      border-color: var(--border-color, #333333);
    }

    .skeleton-image {
      background: linear-gradient(to right, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
    }

    .skeleton-title {
      background: linear-gradient(to right, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
    }

    .skeleton-description {
      background: linear-gradient(to right, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
    }
  }
}

// Pages generated container styles
.pages-generated-container {
  height: 80vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: var(--background-color, #ffffff);
  color: var(--body-text-color, #333333);
  padding: 20px;

  // Skeleton UI styles
  .skeleton-card {
    background-color: var(--card-bg, #ffffff);
  }

  .skeleton-image {
    height: 180px;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  .skeleton-title {
    height: 24px;
    margin: 10px;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  .skeleton-description {
    height: 16px;
    margin: 0 10px 15px;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    animation: shimmer 1.5s infinite linear;
  }

  .pages-generated-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;

    h3 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--body-text-color, #333333);
    }

    p {
      font-size: 14px;
      color: var(--text-secondary, #6c757d);
      line-height: 1.5;
    }
  }

  .pages-examples-grid {
    display: grid;
    gap: 20px;
    padding: 15px;

    // Dynamic grid based on number of layouts
    &.layout-count-1 {
      grid-template-columns: repeat(2, 1fr);

      @media (min-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    &.layout-count-2 {
      grid-template-columns: repeat(2, 1fr);

      @media (min-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    &.layout-count-3,
    &.layout-count-4 {
      grid-template-columns: repeat(2, 1fr);

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    // Default for 5+ layouts or when no layout count class is applied
    &:not(.layout-count-1):not(.layout-count-2):not(.layout-count-3):not(.layout-count-4) {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .page-example-item {
      // Full width item for single layout
      &.full-width {
        grid-column: 1 / -1; // Span all columns

        .page-example-card {
          max-width: 600px;
          margin: 0 auto;
        }
      }

      .page-example-card {
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 8px;
        overflow: hidden;
        transition:
          transform 0.2s ease,
          box-shadow 0.2s ease;
        background-color: var(--card-bg, #ffffff);

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .page-example-image {
          height: 180px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--card-image-bg, #f5f5f5);

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.05);
            }
          }
        }

        .page-example-title {
          padding: 10px;
          text-align: center;
          font-weight: 500;
          font-size: 16px;
          color: var(--body-text-color, #333333);
        }

        .page-example-description {
          padding: 0 10px 15px;
          text-align: center;
          font-size: 14px;
          color: var(--text-secondary, #6c757d);
        }
      }
    }
  }

  iframe {
    flex: 1;
    border: none;
    margin-top: 20px;
    min-height: 400px;
  }

  // Dark theme support
  .dark-theme & {
    background-color: var(--background-color, #121212);

    .pages-generated-header {
      h3 {
        color: var(--body-text-color, #e0e0e0);
      }

      p {
        color: var(--text-secondary, #a0a0a0);
      }
    }

    .page-example-card {
      background-color: var(--card-bg, #1e1e1e);
      border-color: var(--border-color, #333333);

      .page-example-image {
        background-color: var(--card-image-bg, #2a2a2a);
      }

      .page-example-title {
        color: var(--body-text-color, #e0e0e0);
      }

      .page-example-description {
        color: var(--text-secondary, #a0a0a0);
      }
    }

    // Dark theme skeleton UI
    .skeleton-card {
      background-color: var(--card-bg, #1e1e1e);
      border-color: var(--border-color, #333333);
    }

    .skeleton-image {
      background: linear-gradient(to right, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
    }

    .skeleton-title {
      background: linear-gradient(to right, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
    }

    .skeleton-description {
      background: linear-gradient(to right, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
    }
  }
}

// Enhanced URL validation container styles
.url-validation-container {
  height: 80vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  // align-items: center;
  justify-content: center;
  // padding: 2rem;
  background: var(--background-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .url-validation-error {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 6px;
    color: #dc3545;
    max-width: 400px;
    text-align: center;

    .error-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;

      i {
        font-size: 1.1rem;
      }
    }
  }

  // Dark theme support
  :host-context(.dark-theme) & {
    background: var(--background-color);

    .url-validation-error {
      background: rgba(220, 53, 69, 0.2);
      border-color: rgba(220, 53, 69, 0.4);
      color: #ff6b6b;
    }
  }
}

// Iframe container for when code is generated
.iframe-container {
  height: 80vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  // min-height: calc(100vh - 120px); // Account for headers and padding

  iframe {
    flex: 1;
    border: none;
    min-height: 400px;
    width: 100%;
    height: 100%;
    background-color: white;

    // Ensure proper HTML rendering
    &[srcdoc] {
      // Force iframe to render HTML with proper styling
      background: white;
    }
  }
}

// Loading state container styles
.loading-state-container {
  height: 80vh;
  width: 100%;
  // min-height: calc(100vh - 120px); // Match iframe container height
  background-color: transparent;
  color: var(--loading-text-color);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

// Deployment loading container styles
.deployment-loading-container {
  height: 80vh;
  width: 100%;
  // min-height: calc(100vh - 120px); // Match iframe container height
  background-color: transparent;
  color: var(--loading-text-color);
  text-align: center;
  // display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(101, 102, 205, 0.05) 0%,
      rgba(101, 102, 205, 0.02) 50%,
      rgba(101, 102, 205, 0.05) 100%
    );
    pointer-events: none;
    z-index: 0;
  }

  // Ensure loading animation is above the background
  app-loading-animation {
    position: relative;
    z-index: 1;
  }
}

// Preview error styles
.preview-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  // min-height: calc(100vh - 120px); // Match iframe container height
  background-color: var(--preview-page-bg-color);
  border: 0.5px solid #808080;
  color: var(--body-text-color, #ffffff);

  .preview-error-content {
    text-align: center;
    padding: 2rem;

    i {
      font-size: 3rem;
      display: block;
    }

    h4 {
      margin: 1rem 0;
      color: var(--body-text-color, #ffffff);
    }
  }
}

// Deep styles for external components
:host ::ng-deep {
  // Chat wrapper styles
  .chat-wrapper {
    height: 80vh;
    // min-height: calc(100vh - 120px); // Match right panel height

    &.dark {
      border: 1px solid var(--awe-split-border-color);
      background: var(--code-viewer-bg);
    }

    &.light {
      border-radius: 0 0 12px 12px !important;
      border: 1px solid var(--awe-split-border-color);
      background: var(--code-viewer-bg) !important;
    }

    // Modal styles for light and dark themes
    awe-modal {
      // Modal overlay styling
      .modal-overlay {
        background-color: rgba(0, 0, 0, 0.5) !important;
        backdrop-filter: blur(4px) !important;
        animation: fadeIn 0.3s ease-in-out !important;
      }

      // Modal content animation
      .modal-content {
        animation: slideIn 0.3s ease-out !important;
        transition: all 0.3s ease !important;
      }

      // Animations
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        awe-heading {
          color: var(--body-text-color, #333) !important;
        }

        awe-icons {
          cursor: pointer;
          color: var(--body-text-color, #333);

          &:hover {
            opacity: 0.8;
          }
        }
      }

      // Light theme specific modal styles
      &.light {
        .modal-content {
          background-color: var(--background-color, #fff) !important;
          color: var(--body-text-color, #333) !important;
        }

        awe-heading {
          color: var(--body-text-color, #333) !important;
        }
      }

      // Dark theme specific modal styles
      &.dark {
        .modal-content {
          background-color: var(--background-color, #121212) !important;
          color: var(--body-text-color, #e0e0e0) !important;
          border-color: var(--awe-split-border-color, #292c3d) !important;
        }

        awe-heading {
          color: var(--body-text-color, #e0e0e0) !important;
        }

        .input {
          background-color: var(--toggle-button-background-color, #1a1c25) !important;
          color: var(--body-text-color, #e0e0e0) !important;
          border-color: var(--awe-split-border-color, #292c3d) !important;
        }

        // Button styling for dark theme
        awe-button {
          &[variant='secondary'] {
            background-color: var(--toggle-button-background-color, #1a1c25) !important;
            border-color: var(--awe-split-border-color, #292c3d) !important;

            &:hover {
              background-color: var(--code-viewer-file-hover, rgba(255, 255, 255, 0.1)) !important;
            }
          }
        }

        // Button label styling for dark theme
        .button-label {
          color: var(--body-text-color, #e0e0e0) !important;
        }
      }
    }

    // Export modal styles - moved to global scope
  }

  // Card styles
  .awe-card.awe-card--small.awe-card--light.awe-card--basic {
    border-radius: var(--3x, 8px) !important;
    color: var(--chat-window-text-color) !important;
  }

  .user-card.light .awe-card {
    background-color: var(--Neutral-N-100, var(--color-background-light)) !important;
    color: var(--text-black) !important;
  }

  .user-card.dark .awe-card {
    background-color: var(--chat-window-card-bg-color) !important;
    border: 1px solid var(--awe-split-border-color) !important;
    backdrop-filter: blur(60px);
  }

  // AI card styles
  .ai-card.light .awe-card {
    background-color: var(--Neutral-N-50, var(--color-background-light)) !important;
    color: var(--text-black) !important;
  }

  .ai-card.dark .awe-card {
    background-color: var(--chat-window-card-bg-color) !important;
    border: 1px solid var(--awe-split-border-color) !important;
    backdrop-filter: blur(60px);
  }

  // Markdown content styles
  .markdown-content {
    width: 100%;

    markdown {
      display: block;
      width: 100%;

      // Headings
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: 600;
        line-height: 1.25;
      }

      h1 {
        font-size: 1.5em;
        border-bottom: 1px solid var(--color-border-light);
        padding-bottom: 0.3em;
      }

      h2 {
        font-size: 1.25em;
        border-bottom: 1px solid var(--color-border-light);
        padding-bottom: 0.3em;
      }

      h3 {
        font-size: 1.1em;
      }

      // Lists
      ul,
      ol {
        padding-left: 2em;
        margin-top: 0.5em;
        margin-bottom: 0.5em;
      }

      li {
        margin: 0.25em 0;
      }

      // Code blocks
      pre {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: 0.5em;
        overflow-x: auto;
        margin: 0.5em 0;

        code {
          background-color: transparent;
          padding: 0;
          border-radius: 0;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 0.9em;

          // Enable text selection in code blocks
          user-select: text !important;
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;

          // Syntax highlighting
          .hljs-keyword {
            color: #569cd6;
          }

          .hljs-string {
            color: #ce9178;
          }

          .hljs-comment {
            color: #6a9955;
          }

          .hljs-function {
            color: #dcdcaa;
          }

          .hljs-number {
            color: #b5cea8;
          }

          .hljs-operator {
            color: #d4d4d4;
          }

          .hljs-class {
            color: #4ec9b0;
          }

          .hljs-variable {
            color: #9cdcfe;
          }
        }
      }

      code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        padding: 0.2em 0.4em;
        font-size: 0.9em;
      }

      // Tables
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 1em 0;
      }

      th,
      td {
        border: 1px solid var(--color-border-light);
        padding: 0.5em;
        text-align: left;
      }

      th {
        background-color: rgba(0, 0, 0, 0.05);
      }

      // Blockquotes
      blockquote {
        border-left: 4px solid var(--color-border-light);
        margin: 0.5em 0;
        padding: 0 1em;
        color: var(--text-secondary);
      }

      // Links
      a {
        color: var(--color-primary);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      // Images
      img {
        max-width: 100%;
        height: auto;
        margin: 0.5em 0;
      }

      // Horizontal rule
      hr {
        border: 0;
        border-top: 1px solid var(--color-border-light);
        margin: 1em 0;
      }

      // Paragraphs
      p {
        margin: 0.5em 0;
        line-height: 1.5;
      }
    }
  }

  // Loading styles
  .loading-container {
    height: 80vh !important;
    // min-height: calc(100vh - 120px) !important; // Match other containers
  }

  // Split screen styles with maximum hardware acceleration
  .awe-splitscreen {
    position: relative;
    will-change: contents, transform; // Optimize for animations
    touch-action: none; // Prevent touch scrolling during resize
    transform: translateZ(0); // Force hardware acceleration
    backface-visibility: hidden; // Prevent flickering
    -webkit-font-smoothing: antialiased; // Smoother text rendering
    -moz-osx-font-smoothing: grayscale; // Smoother text rendering in Firefox
    contain: layout paint style; // Contain repaints for better performance

    .resizer {
      width: 0px; // Original thin width
      background-color: transparent; // Completely transparent
      cursor: ew-resize;
      position: relative;
      z-index: 100; // Ensure resizer is above other elements
      transform: translateZ(0); // Force hardware acceleration
      will-change: transform; // Optimize for animations
      touch-action: none; // Prevent touch scrolling during resize
      user-select: none !important; // Prevent text selection
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;

      // Improved touch target for mobile (invisible)
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -10px;
        width: 20px; // Wider touch target but invisible
        height: 100%;
        z-index: 99;
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
      }

      // Subtle line on hover - will be colored based on theme
      &:hover {
        background-color: transparent;
      }
    }

    // Hide resizer when left panel is collapsed
    &.left-panel-collapsed {
      .resizer {
        display: none;
        cursor: default;
      }
    }

    // Maximum hardware acceleration for ultra-smooth panel width changes
    .awe-leftpanel,
    .awe-rightpanel {
      will-change: width, transform; // Optimize for animations
      transition: width 0.08s cubic-bezier(0.4, 0, 0.2, 1); // Faster Material Design easing
      transform: translateZ(0) scale(1); // Force hardware acceleration
      backface-visibility: hidden; // Prevent flickering
      perspective: 1000px; // Enhance 3D rendering
      -webkit-font-smoothing: antialiased; // Smoother text rendering
      -moz-osx-font-smoothing: grayscale; // Smoother text rendering in Firefox
      contain: layout paint style; // Contain repaints for better performance

      // Ensure both panels have consistent height
      // height: 100vh;
      // min-height: 100vh;

      // Force hardware acceleration for all child elements
      * {
        transform: translateZ(0);
        backface-visibility: hidden;
        will-change: transform;
        transition: inherit; // Inherit transition from parent
      }

      // Ensure content areas fill the available height
      [awe-leftpanel-content],
      [awe-rightpanel-content] {
        display: flex;
        flex-direction: column;
      }

      // Specific styling for right panel to prevent scrolling
      [awe-rightpanel-content] {
        overflow: hidden; // Prevent scrolling in right panel

        // Ensure mobile frame container fits perfectly
        .mobile-frame-container {
          overflow: hidden;
          height: 80vh;
        }
      }

      // Left panel can have scrolling for code content
      [awe-leftpanel-content] {
        overflow-y: auto;
      }

      // Remove transition during active dragging for instant feedback
      &.dragging {
        transition: none !important;
        will-change: width, transform; // Optimize for animations
        transform: translateZ(0) scale(1); // Force hardware acceleration

        // Force hardware acceleration for all child elements during dragging
        * {
          transition: none !important;
          will-change: transform;
          transform: translateZ(0);
        }
      }

      // Prevent text selection only for UI controls, not content
      .custom-header,
      .tabs-container,
      .custom-button,
      awe-icons,
      .icon-group,
      .step-timer,
      .card-container,
      .option-card,
      .data-card,
      .create-card {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }

      // Allow text selection in content areas
      .markdown-content,
      .chat-messages,
      .log-content,
      .log-text,
      .code-content,
      pre,
      code,
      .formatted-logs-container,
      .artifacts-content,
      .file-content,
      .text-content {
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
      }

      // Force all content to resize properly
      img,
      video,
      canvas,
      iframe,
      svg {
        max-width: 100%;
        // height:auto;
        transform: translateZ(0);
      }

      // Ensure Monaco editor resizes properly
      .monaco-editor,
      .monaco-editor-background,
      .monaco-editor .margin,
      .monaco-workbench {
        will-change: width, transform;
        transition: width 0.08s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .awe-leftpanel-header,
    .awe-rightpanel-header {
      padding: 0.25rem;
      pointer-events: auto; // Ensure header elements remain clickable
    }

    .awe-leftpanel-content,
    .awe-rightpanel-content {
      padding: 0.25rem;
      overflow: hidden; // Prevent content overflow during resize
      transform: translateZ(0); // Force hardware acceleration
      will-change: transform; // Optimize for animations
      transition: inherit; // Inherit transition from parent

      // Force all content to resize properly
      * {
        transform: translateZ(0);
        will-change: transform;
        transition: inherit;
      }
    }
  }

  // Prevent text selection during resize with maximum effectiveness
  .user-select-none {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    cursor: col-resize !important;
    touch-action: none !important;

    // Prevent text selection on all elements by default
    * {
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      pointer-events: none !important;
    }

    // Apply to UI elements only, not content areas
    .custom-header,
    .tabs-container,
    .custom-button,
    awe-icons,
    .icon-group,
    .resizer,
    .step-timer,
    .card-container,
    .option-card,
    .data-card,
    .create-card {
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      pointer-events: none !important;
    }

    // Allow pointer events and text selection in content areas ONLY when not resizing
    .markdown-content,
    .chat-messages,
    .log-content,
    .log-text,
    .code-content,
    pre,
    code,
    .formatted-logs-container,
    .artifacts-content,
    .file-content,
    .text-content {
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      pointer-events: none !important;
    }

    // Allow pointer events on specific elements that need to function during resize
    .resizer,
    .awe-leftpanel,
    .awe-rightpanel {
      pointer-events: auto !important;
      touch-action: none !important;
      will-change: transform, width !important;
      transform: translateZ(0) !important;
    }
  }
}

.adjust-height {
  height: 100%;
}

// Global text selection prevention for cards and UI elements
.card-container,
.option-card,
.data-card,
.create-card,
.step-timer,
.custom-button,
.tabs-container,
.custom-header {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  // Prevent text selection on all child elements by default
  * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }

  // Allow text selection only in specific content areas within cards
  .card-description,
  .card-content,
  .text-content,
  .markdown-content,
  input,
  textarea {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }
}

// Smooth dragging enhancements
.smooth-split-screen.resizing {
  // Apply hardware acceleration to the entire container during resize
  transform: translateZ(0) scale(1);
  will-change: transform;
  cursor: col-resize !important;
  overflow: hidden;
  * {
    transition: none !important;
    animation: none !important;
    will-change: transform, width;
    transform: translateZ(0);
  }

  // Force GPU acceleration on panels
  .awe-leftpanel,
  .awe-rightpanel {
    transform: translateZ(0) scale(1);
    will-change: width, transform;
    backface-visibility: hidden;
  }

  // Specifically disable iframe interactions during resize
  iframe,
  .preview-frame,
  .iframe-container {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }
}

// Global resizing state for maximum text selection prevention
:host-context(.resizing-active) {
  * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    cursor: col-resize !important;
  }
}

// Global resizing state applied to html element
html.resizing-active {
  * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    cursor: col-resize !important;
    pointer-events: none !important;
  }

  // Specifically disable iframe pointer events during resize
  iframe {
    pointer-events: none !important;
    user-select: none !important;
  }

  // Allow only the resizer and panels to have pointer events
  .resizer,
  .awe-leftpanel,
  .awe-rightpanel,
  .smooth-split-screen {
    pointer-events: auto !important;
  }
}

// Responsive styles for left panel width-based hiding
.awe-leftpanel {
  // When left panel is narrow (less than 400px), hide the project name
  &[style*="width: 3"] .header-center .project-name,
  &[style*="width: 2"] .header-center .project-name,
  &[style*="width: 1"] .header-center .project-name {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
  }

  // More specific targeting for very narrow widths
  &[style*="300px"] .header-center .project-name,
  &[style*="250px"] .header-center .project-name,
  &[style*="200px"] .header-center .project-name,
  &[style*="150px"] .header-center .project-name {
    display: none;
  }
}

// Container query approach for better responsiveness
@container (max-width: 400px) {
  .custom-header .header-center .project-name {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
  }
}

@container (max-width: 350px) {
  .custom-header .header-center .project-name {
    display: none;
  }
}

// Responsive styles - simplified
@media (max-width: 768px) {
  .custom-header {
    padding: 0 8px;
  }
  .custom-button {
    padding: 6px 12px;
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .custom-header {
    flex-direction: column;
    height: auto;
    padding: 8px;
    .header-left,
    .header-right {
      width: 100%;
    }
  }
}

::ng-deep .prompt-bar.chat-bot.light awe-icons,
::ng-deep .prompt-bar.chat-bot.dark awe-icons {
  path {
    fill: var(--chat-window-icon-color) !important;
  }
}

::ng-deep .modal.custom-variant {
  height: auto !important;
  width: 500px !important;
  max-width: 90vw !important;
  border-radius: 16px !important;
  overflow: hidden !important;
}

::ng-deep .modal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 20px 24px !important;
  border-bottom: 1px solid #eaeaea !important;
}

::ng-deep .modal-header p.h3.heading.bold {
  color: var(--modal-text-color, #333) !important;
  font-size: 20px !important;
  margin: 0 !important;
}

::ng-deep .modal-content {
  padding: 0 !important;
  background-color: #f5f5f5 !important;
}

::ng-deep .button-pill-grid {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}

::ng-deep .modal {
  background-color: #f5f5f5 !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

::ng-deep .button-label {
  color: var(--modal-text-color, #333) !important;
}

// Dark theme modal styles
::ng-deep .dark-theme .modal {
  background-color: #2a2a2a !important;
}

::ng-deep .dark-theme .modal-content {
  background-color: #2a2a2a !important;
}

::ng-deep .dark-theme .modal-header {
  border-bottom-color: #444 !important;
}

::ng-deep .dark-theme .modal-header p.h3.heading.bold {
  color: var(--modal-text-color, #e0e0e0) !important;
}

::ng-deep .chat-messages {
  margin-bottom: 1rem !important;
}
::ng-deep .chat-wrapper {
  padding-bottom: 0px !important;
}

::ng-deep .prompt-bar .prompt-text {
  font-weight: normal !important;
}

::ng-deep .prompt-bar.chat-bot.dark {
  background: transparent !important;
  border: 2px solid var(--prompt-bar-border-color) !important;
  border-radius: 12px !important;
  transition: border-color 0.3s ease !important;

  &:hover,
  &:focus-within {
    border-color: var(--prompt-bar-hover-color) !important;
  }
}

::ng-deep .prompt-bar.chat-bot.light {
  background: transparent !important;
  border: 2px solid var(--prompt-bar-border-color) !important;
  border-radius: 12px !important;
  transition: border-color 0.3s ease !important;

  &:hover,
  &:focus-within {
    border-color: var(--prompt-bar-hover-color) !important;
  }
}

// Ensure the prompt bar has proper spacing
::ng-deep .prompt-bar.chat-bot {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
  padding: 8px !important;
}
.ai-card.dark :host ::ng-deep .awe-card__content {
  padding: 0px 10px !important;
}

// Logs container and header styles
.logs-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
  background-color: var(--logs-container-bg, #f5f5f5);
  border: 1px solid var(--logs-container-border, #e0e0e0);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
}

// Logs header styles
.logs-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--logs-header-border, #e0e0e0);
  background-color: var(--logs-header-bg, #f8f8f8);

  .logs-header-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .logs-header-left {
    display: flex;
    align-items: center;
    min-width: 180px;

    .logs-header-title {
      display: flex;
      align-items: center;
      gap: 8px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .logs-header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .log-filter {
      display: flex;
      align-items: center;
      gap: 16px;

      .filter-option {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.2s ease;

        &:hover:not(.disabled) {
          background-color: rgba(0, 0, 0, 0.05);
        }

        &.active {
          background-color: rgba(0, 0, 0, 0.08);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }

        .info-dot {
          background-color: var(--logs-info-color, #2196f3);
        }
        .debug-dot {
          background-color: var(--logs-debug-color, #4caf50);
        }
        .warning-dot {
          background-color: var(--logs-warning-color, #ff9800);
        }
        .error-dot {
          background-color: var(--logs-error-color, #f44336);
        }
      }
    }
  }

  .logs-header-right {
    display: flex;
    align-items: center;
    min-width: 80px;

    .log-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      awe-icons {
        cursor: pointer;

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Logs content styles
.logs-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: var(--logs-content-bg, #ffffff);

  // Enable text selection in logs content
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;

  .logs-info-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: rgba(33, 150, 243, 0.1);
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 13px;
    color: var(--logs-info-color, #2196f3);
  }

  pre {
    margin: 0;
    padding: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    height: 100%;
    overflow-y: auto;

    code {
      display: block;
      padding: 4px 0;
      line-height: 1.5;
      position: relative;

      // Add a subtle animation for new logs
      &:last-child {
        animation: fadeInLog 0.5s ease-in-out;
      }
    }
  }
}

// Formatted logs container
.formatted-logs-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;

  // Message for when no logs are available
  .no-logs-message {
    padding: 16px;
    text-align: center;
    color: #666;
    font-style: italic;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    margin: 16px 0;
  }
}

// Log entry styling
.log-entry {
  display: flex;
  flex-direction: column;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  margin-bottom: 4px;
  border-left: 3px solid transparent;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  // Highlight new logs with a subtle animation
  &.new-log {
    animation: highlightNewLog 2s ease-out;
    border-left-color: var(--logs-info-color, #2196f3);
  }

  // Timestamp styling
  .log-timestamp {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  }

  // Log content styling with enhanced typewriter effect
  .log-content {
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
    display: flex;
    align-items: center;
    position: relative; // For typing indicator

    .log-text {
      display: inline-block;

      // Enable text selection in log text
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }

    // Add a dramatic highlight effect when typing
    &.typing {
      .log-text {
        position: relative;

        // Add a subtle background highlight to the entire text
        background-color: rgba(33, 150, 243, 0.05);
        border-radius: 3px;
        padding: 0 2px;

        // Create a character-by-character animation effect
        // This makes each character appear with a subtle fade-in
        display: inline-block;

        // Add a highlight to the last character
        &::after {
          content: '';
          position: absolute;
          right: -2px;
          top: 0;
          height: 100%;
          width: 3px;
          background-color: var(--logs-info-color, #2196f3);
          opacity: 0.6;
          animation: pulseHighlight 0.8s infinite;
          border-radius: 1px;
          box-shadow: 0 0 8px rgba(33, 150, 243, 0.8);
        }

        // Apply a subtle animation to the last character
        &:last-letter {
          color: var(--logs-info-color, #2196f3);
          text-shadow: 0 0 3px rgba(33, 150, 243, 0.8);
          animation: characterAppear 0.3s ease-out;
        }
      }
    }
  }
}

// Animation for highlighting new logs
@keyframes highlightNewLog {
  0% {
    background-color: rgba(33, 150, 243, 0.1);
  }
  100% {
    background-color: rgba(0, 0, 0, 0.02);
  }
}

// Code capsule styling
.code-capsule {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--logs-container-border, #e0e0e0);
  margin: 8px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  transition: all 0.3s ease;

  // Code header styling
  .code-header {
    background-color: var(--logs-header-bg, #f5f5f5);
    padding: 8px 12px;
    border-bottom: 1px solid var(--logs-container-border, #e0e0e0);
    font-size: 13px;
    font-weight: 500;
    color: var(--logs-text-color, #333);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    user-select: none; // Only for the header itself, not the content
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--logs-filter-hover-bg, rgba(0, 0, 0, 0.05));
    }

    .file-path {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      flex: 1;
    }

    .code-header-actions {
      display: flex;
      align-items: center;

      .toggle-icon {
        font-size: 10px;
        margin-left: 8px;
        transition: transform 0.2s ease;
      }
    }
  }

  // Code content wrapper for animation
  .code-content-wrapper {
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease-in-out;
    background-color: var(--logs-content-bg, #fafafa);
    opacity: 0;
    // Default CSS variable for max height
    --max-content-height: 500px;

    &.expanded {
      max-height: var(--max-content-height); // Use dynamic height from component
      opacity: 1;
      overflow-y: auto;
      transition:
        max-height 0.8s ease-in-out,
        opacity 0.5s ease-in-out; // Smoother transition
    }
  }

  // Code content styling with enhanced typewriter effect
  .code-content {
    margin: 0;
    padding: 12px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.5;
    position: relative; // For proper cursor positioning
    display: flex; // For better cursor alignment
    flex-direction: column;

    code {
      white-space: pre-wrap;
      word-break: break-word;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      display: block;
      width: 100%;
      color: var(--logs-text-color, #333);
      position: relative; // For proper cursor positioning

      // Enable text selection in log code blocks
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;

      // Add a dramatic highlight to the most recently typed character
      &.typing {
        // Add a subtle background highlight to the entire code block
        background-color: rgba(33, 150, 243, 0.05);
        border-radius: 3px;

        // Create a character-by-character animation effect
        letter-spacing: 0.5px;

        &::after {
          content: '';
          position: absolute;
          right: 0;
          bottom: 0;
          width: 4px;
          height: 100%;
          background-color: rgba(33, 150, 243, 0.6);
          animation: pulseHighlight 0.8s infinite;
          border-radius: 1px;
          box-shadow: 0 0 10px rgba(33, 150, 243, 0.8);
        }
      }

      // Enhanced styling for syntax highlighting during typewriting
      ::ng-deep span {
        transition: all 0.3s ease-in-out;

        // Add a dramatic highlight effect to newly typed characters
        &:last-child {
          color: var(--logs-info-color, #2196f3);
          text-shadow: 0 0 4px rgba(33, 150, 243, 0.8);
          background-color: rgba(33, 150, 243, 0.1);
          border-radius: 2px;
          padding: 0 2px;
          animation: characterAppear 0.3s ease-out;
          font-weight: bold;
        }
      }

      // Add a special effect for keywords, strings, and other syntax elements
      ::ng-deep {
        .hljs-keyword,
        .hljs-string,
        .hljs-function,
        .hljs-number {
          &:last-child {
            animation: characterAppear 0.4s ease-out;
            text-shadow: 0 0 5px currentColor;
          }
        }
      }

      // Ensure proper display of HTML-formatted code
      ::ng-deep br {
        display: block;
        content: '';
        margin-top: 0.5em;
      }
    }

    // Enhanced animation for the typing highlight
    @keyframes pulseHighlight {
      0% {
        opacity: 0.3;
        box-shadow: 0 0 3px var(--logs-info-color, #2196f3);
      }
      50% {
        opacity: 0.7;
        box-shadow: 0 0 8px var(--logs-info-color, #2196f3);
      }
      100% {
        opacity: 0.3;
        box-shadow: 0 0 3px var(--logs-info-color, #2196f3);
      }
    }

    // Position the cursor at the end of the text
    .typewriter-cursor {
      position: relative; // Changed from absolute for better positioning
      display: inline-block;
      vertical-align: middle;
      margin-left: 2px;
      height: 18px; // Slightly taller for code blocks
    }
  }

  // Empty state styling
  &:empty {
    display: none;
  }

  // Expanded state
  &.expanded {
    .code-header {
      .toggle-icon {
        transform: rotate(0deg);
      }
    }
  }

  // Dark theme styling
  &.dark-theme {
    border-color: var(--logs-container-border, #292c3d);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

    .code-header {
      background-color: var(--logs-header-bg, #1a1c25);
      border-color: var(--logs-container-border, #292c3d);
      color: var(--logs-text-color, #ffffff);

      &:hover {
        background-color: var(--logs-filter-hover-bg, rgba(255, 255, 255, 0.1));
      }
    }

    .code-content-wrapper {
      background-color: var(--logs-content-bg, #14161f);
    }

    .code-content {
      code {
        color: var(--logs-text-color, #ffffff);
      }
    }
  }
}

// Log message types with improved styling
.log-info {
  color: var(--logs-info-color, #2196f3);
  pointer-events: none;
  cursor: default;

  .log-content::before {
    content: '•';
    margin-right: 6px;
    color: var(--logs-info-color, #2196f3);
  }

  &.progress-description {
    color: var(--logs-info-color, #2196f3); // Use the same color as info logs
    font-weight: 500;

    .log-content::before {
      content: '📝';
      margin-right: 6px;
      color: var(--logs-info-color, #2196f3);
    }
  }

  &.status-change {
    color: var(--logs-info-color, #2196f3); // Use the same color as info logs
    font-weight: 500;

    .log-content::before {
      content: '🔄';
      margin-right: 6px;
      color: var(--logs-info-color, #2196f3);
    }
  }
}

.log-debug {
  color: var(--logs-info-color, #2196f3); // Use the same color as info logs
  pointer-events: none;
  cursor: default;

  .log-content::before {
    content: '•';
    margin-right: 6px;
    color: var(--logs-info-color, #2196f3);
  }
}

.log-warning {
  color: var(--logs-warning-color, #ff9800);
  pointer-events: none;
  cursor: default;

  .log-content::before {
    content: '⚠';
    margin-right: 6px;
    color: var(--logs-warning-color, #ff9800);
  }
}

.log-error {
  color: var(--logs-error-color, #f44336);
  font-weight: bold;
  pointer-events: none;
  cursor: default;

  .log-content::before {
    content: '✖';
    margin-right: 6px;
    color: var(--logs-error-color, #f44336);
  }
}

// Code log styling
.log-code {
  color: var(--logs-info-color, #2196f3); // Use the same color as info logs
}

// Enhanced animation for log entries - smoother and more dynamic
@keyframes fadeInLog {
  0% {
    opacity: 0;
    transform: translateY(8px);
    filter: blur(2px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(3px);
    filter: blur(0.5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

// Animation for the streamline effect - applied to new log entries
.new-log {
  animation: fadeInLog 0.3s ease-out forwards;
}

// Animation for typewriter cursor - dramatically enhanced for maximum visibility
.typewriter-cursor {
  display: inline-block;
  width: 5px; // Wider for better visibility
  height: 22px; // Taller for better visibility
  background-color: var(--logs-info-color, #2196f3); // Use the same color as info logs
  margin-left: 3px;
  vertical-align: middle;
  animation: blink 0.6s infinite; // Faster blinking for more attention
  position: relative;
  top: 2px;
  border-radius: 2px; // More rounded corners
  box-shadow:
    0 0 12px rgba(33, 150, 243, 0.9),
    0 0 5px rgba(33, 150, 243, 1); // Double glow effect
  z-index: 10; // Ensure cursor is always visible

  // Add a pseudo-element for an additional visual effect
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: transparent;
    border: 1px solid rgba(33, 150, 243, 0.5);
    border-radius: 3px;
    animation: pulse 1.5s infinite;
  }

  // Special styling for code content
  .code-content & {
    height: 24px; // Taller in code blocks
    width: 6px; // Wider in code blocks
    background-color: var(--logs-info-color, #2196f3); // Use the same color as info logs
    top: 3px;
    box-shadow:
      0 0 15px rgba(33, 150, 243, 1),
      0 0 5px rgba(255, 255, 255, 0.5); // Enhanced glow for better visibility
    border-radius: 2px; // More rounded corners in code blocks
  }

  // Make the cursor more visible in dark theme
  .dark-theme & {
    background-color: var(--logs-info-color, #2196f3); // Use the same color as info logs
    box-shadow:
      0 0 15px rgba(33, 150, 243, 1),
      0 0 8px rgba(255, 255, 255, 0.7); // Enhanced glow for dark theme
  }

  // Special styling for error logs
  .log-error & {
    background-color: var(--logs-error-color, #f44336); // Red cursor for error logs
    box-shadow:
      0 0 15px rgba(244, 67, 54, 1),
      0 0 5px rgba(255, 255, 255, 0.5); // Red glow for error logs

    &::before {
      border-color: rgba(244, 67, 54, 0.5);
    }
  }

  // Special styling for warning logs
  .log-warning & {
    background-color: var(--logs-warning-color, #ff9800); // Orange cursor for warning logs
    box-shadow:
      0 0 15px rgba(255, 152, 0, 1),
      0 0 5px rgba(255, 255, 255, 0.5); // Orange glow for warning logs

    &::before {
      border-color: rgba(255, 152, 0, 0.5);
    }
  }
}

// Enhanced blinking animation for the cursor
@keyframes blink {
  0%,
  100% {
    opacity: 1;
    transform: scaleY(1);
    box-shadow: 0 0 10px currentColor; // Expanded glow at full opacity
  }
  50% {
    opacity: 0.5; // More pronounced fade for better visibility
    transform: scaleY(0.85); // More pronounced scaling for a more dynamic effect
    box-shadow: 0 0 5px currentColor; // Reduced glow at low opacity
  }
}

// Animation for character appearance
@keyframes characterAppear {
  0% {
    opacity: 0;
    transform: scale(1.5);
    text-shadow: 0 0 10px var(--logs-info-color, #2196f3);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    text-shadow: 0 0 5px var(--logs-info-color, #2196f3);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    text-shadow: 0 0 2px var(--logs-info-color, #2196f3);
  }
}
::ng-deep .awe-splitscreen .resizer:after {
  width: 2px !important;
}
::ng-deep .awe-card--light{
  box-shadow: none !important;
  border:1px solid var(--code-viewer-border) !important;
}


::ng-deep .awe-splitscreen .awe-leftpanel{
  border-right: none !important
}

// ===== PANEL HEIGHT ALIGNMENT STYLES =====

// Ensure all main content containers have consistent heights
.editor-view,
.logs-view,
.artifacts-view {
  height: 100%;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

// Ensure pages generated container matches other containers
.pages-generated-container {
  height: 100%;
  min-height: calc(100vh - 120px);
  width: 100%;
  padding: 20px;
  background-color: transparent;
  color: var(--body-text-color, #333333);
  overflow-y: auto;
  position: relative;
}

// Ensure adjust-height class works properly
.adjust-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// ===== HTML CONTENT RENDERING FIXES =====

// Ensure iframes render HTML content properly
iframe[srcdoc] {
  background: white !important;

  // Inject basic CSS reset for proper HTML rendering
  &::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }
}

// Global iframe styles for HTML content
iframe {
  // Ensure proper content rendering
  &[srcdoc] {
    // Force white background for HTML content
    background-color: white !important;
  }

  // Ensure iframe content is properly styled
  body {
    margin: 0 !important;
    padding: 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
    line-height: 1.5 !important;
    color: #333 !important;
  }
}

// ===== UI DESIGN CANVAS STYLES =====

.ui-design-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;

  // Professional dot grid background (Figma/Sketch style)
  background-color: var(--canvas-bg, #ffffff);
  background-image:
    radial-gradient(
      circle at var(--canvas-dot-size, 1px) var(--canvas-dot-size, 1px),
      var(--canvas-dot-color, rgba(0, 0, 0, 0.08)) 1px,
      transparent 0
    );
  background-size: var(--canvas-grid-size, 20px) var(--canvas-grid-size, 20px);
  background-position: 0 0;

  // Smooth transitions for theme changes
  transition: background-color 0.3s ease, background-image 0.3s ease;

  .canvas-controls {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 10;
    display: flex;
    gap: 12px;

    .zoom-controls,
    .view-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      background: var(--canvas-controls-bg, rgba(255, 255, 255, 0.95));
      border: 1px solid var(--canvas-controls-border, #e1e5e9);
      border-radius: 8px;
      padding: 8px 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .canvas-control-btn {
      background: transparent;
      border: 1px solid var(--canvas-btn-border, #d1d5db);
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 12px;
      font-weight: 500;
      color: var(--canvas-btn-color, #374151);
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 4px;

      &:hover:not(:disabled) {
        background: var(--canvas-btn-hover-bg, #f3f4f6);
        border-color: var(--canvas-btn-hover-border, #9ca3af);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        .zoom-icon {
          opacity: 0.5;
        }
      }

      i {
        font-size: 14px;
      }

      // Custom zoom icon styling
      .zoom-icon {
        width: 16px;
        height: 16px;
        transition: all 0.2s ease;

        // Ensure SVG scales properly
        flex-shrink: 0;

        // Ensure visibility with explicit stroke properties
        stroke: var(--canvas-icon-color, #1f2937);
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;

        // Apply stroke color to all paths and shapes
        circle, path {
          stroke: var(--canvas-icon-color, #1f2937);
          fill: none;
        }

        // Smooth color transitions for theme changes
        * {
          transition: stroke 0.2s ease;
        }
      }

      &:hover:not(:disabled) .zoom-icon {
        transform: scale(1.1);
      }
    }

    .zoom-display {
      font-size: 12px;
      font-weight: 600;
      color: var(--canvas-zoom-color, #6b7280);
      min-width: 40px;
      text-align: center;
    }
  }

  .canvas-content {
    position: relative;
    width: 100%;
    height: 100%;
    transform-origin: 0 0;
    transition: transform 0.2s ease;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }
}

.ui-design-node {
  position: absolute;
  border-radius: 12px;
  background-color: var(--design-card-bg);
  border: 1px solid var(--design-card-border);
  width: 100%;
  height: 100%;
  box-shadow: var(--design-card-shadow);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;



  // Card title section
  .card-title {
    background: var(--design-card-title-bg);
    padding: 16px 20px;
    border-bottom: 1px solid var(--design-card-border);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--design-card-title-text);
      text-align: center;
      line-height: 1.4;
    }
  }

  &:hover {
    border-color: var(--design-card-border-hover);
    box-shadow: var(--design-card-shadow-hover);
    transform: translateY(-2px);

    .node-overlay {
      opacity: 1;
    }
  }

  // Enhanced selection for editing state - consolidated selection styling
  &.selected-for-editing {
    border: 3px solid var(--node-selected-editing-border);
    box-shadow:
      0 0 0 6px var(--node-selected-editing-shadow),
      0 12px 32px rgba(0, 0, 0, 0.2);
    transform: translateY(-3px) scale(1.03);
    z-index: 10;
    position: relative;

    // Add a prominent glow effect
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      background: linear-gradient(45deg, var(--node-selected-editing-border), transparent, var(--node-selected-editing-border));
      border-radius: 16px;
      z-index: -1;
      opacity: 0.4;
      animation: editingGlow 2s ease-in-out infinite alternate;
    }

    // Add inner highlight for better visibility
    &::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      right: 2px;
      bottom: 2px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 10px;
      z-index: 1;
      pointer-events: none;
    }

    .node-header {
      background: linear-gradient(135deg, var(--node-title-selected-bg), var(--node-title-selected-bg));
      border-radius: 8px 8px 0 0;
      position: relative;
      z-index: 2;

      h4 {
        color: var(--node-title-selected-color);
        background: var(--node-title-selected-bg);
        font-weight: 700;
        padding: 6px 12px;
        border-radius: 6px;
        box-shadow: var(--node-title-selected-shadow);
        margin: 4px;
        display: inline-block;
      }
    }

    // Override hover effects when selected for editing
    &:hover {
      transform: translateY(-3px) scale(1.03);
      border-color: var(--node-selected-editing-border);
      box-shadow:
        0 0 0 6px var(--node-selected-editing-shadow),
        0 16px 40px rgba(0, 0, 0, 0.25);
    }
  }

  &.dragging {
    cursor: grabbing;
    transform: rotate(1deg) scale(1.02);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
  }

  &.loading {
    opacity: 0.8;
    pointer-events: none;

    .node-content {
      background: var(--design-card-content-bg);
    }
  }

  .node-header {
    background: var(--design-card-title-bg);
    border-bottom: 1px solid var(--design-card-border);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    flex-shrink: 0;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--design-card-title-text);
      text-align: center;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  .node-content {
    position: relative;
    flex: 1;
    background: var(--design-card-content-bg);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    .preview-iframe {
      width: 100%;
      height: 100%;
      border: none;
      background: var(--design-card-content-bg);
      border-radius: 8px;
      display: block;
      box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    }

    // Placeholder content when no iframe
    &:empty::before {
      content: 'Design Preview';
      color: var(--design-card-title-text);
      opacity: 0.5;
      font-size: 14px;
      font-weight: 500;
    }

    // Loader styles
    .node-loader {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      gap: 16px;

      .loader-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--design-card-border);
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loader-text {
        font-size: 12px;
        font-weight: 500;
        color: var(--design-card-title-text);
        opacity: 0.7;
        text-align: center;
        animation: pulse 2s ease-in-out infinite;
      }
    }
  }

  .node-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(59, 130, 246, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;

    i {
      font-size: 24px;
      margin-bottom: 8px;
    }

    span {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// ===== UI DESIGN NODE ANIMATIONS =====

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

@keyframes editingGlow {
  0% {
    opacity: 0.2;
    transform: scale(1);
  }
  100% {
    opacity: 0.4;
    transform: scale(1.01);
  }
}

// ===== LOADING NODES FOR REGENERATION =====

.ui-design-node.loading-node {
  position: absolute;
  border-radius: 12px;
  background-color: var(--design-card-bg);
  border: 2px solid var(--regeneration-loading-border, #4CAF50);
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2);
  z-index: 1000;

  // Subtle pulsing animation to indicate loading
  animation: loadingNodePulse 2s ease-in-out infinite;

  .node-header {
    background: linear-gradient(135deg,
      var(--regeneration-header-bg-start, #E8F5E8),
      var(--regeneration-header-bg-end, #F1F8E9));
    border-bottom: 1px solid var(--regeneration-header-border, #C8E6C9);

    h4 {
      color: var(--regeneration-header-text, #2E7D32);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '✏️';
        font-size: 14px;
        animation: editIconBounce 1.5s ease-in-out infinite;
      }
    }
  }

  .node-content {
    position: relative;
    overflow: hidden;

    .regeneration-loader {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: linear-gradient(135deg,
        var(--regeneration-bg-start, #F1F8E9),
        var(--regeneration-bg-end, #E8F5E8));

      .loader-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid var(--regeneration-spinner-bg, #C8E6C9);
        border-top: 4px solid var(--regeneration-spinner-active, #4CAF50);
        border-radius: 50%;
        animation: regenerationSpin 1s linear infinite;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
      }

      .loader-text {
        color: var(--regeneration-text, #2E7D32);
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        animation: loadingTextPulse 1.5s ease-in-out infinite;

        // Add typing dots animation
        &::after {
          content: '';
          animation: typingDots 1.5s steps(4, end) infinite;
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    border-radius: 12px;
    overflow: hidden;

    .loading-pulse {
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(76, 175, 80, 0.1),
        transparent
      );
      animation: loadingPulseMove 3s ease-in-out infinite;
    }
  }

  // Prevent interaction with loading nodes
  pointer-events: none;
  user-select: none;
}

// ===== LOADING NODE ANIMATIONS =====

@keyframes loadingNodePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 25px rgba(76, 175, 80, 0.3);
  }
}

@keyframes regenerationSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes loadingTextPulse {
  0%, 100% {
    opacity: 1;
    transform: translateY(0);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-2px);
  }
}

@keyframes editIconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes typingDots {
  0% { content: ''; }
  25% { content: '.'; }
  50% { content: '..'; }
  75% { content: '...'; }
  100% { content: ''; }
}

@keyframes loadingPulseMove {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

// ===== UI DESIGN NODE DARK THEME SUPPORT =====

:host-context(.dark-theme) {
  .ui-design-node {
    background-color: var(--design-card-bg);
    border-color: var(--design-card-border);
    box-shadow: var(--design-card-shadow);

    &:hover {
      border-color: var(--design-card-border-hover);
      box-shadow: var(--design-card-shadow-hover);
    }

    // Dark theme loading nodes
    &.loading-node {
      background-color: var(--design-card-bg);
      border-color: var(--regeneration-loading-border-dark, #66BB6A);
      box-shadow: 0 4px 20px rgba(102, 187, 106, 0.3);

      .node-header {
        background: linear-gradient(135deg,
          var(--regeneration-header-bg-start-dark, #2E2E2E),
          var(--regeneration-header-bg-end-dark, #3A3A3A));
        border-bottom-color: var(--regeneration-header-border-dark, #4A4A4A);

        h4 {
          color: var(--regeneration-header-text-dark, #81C784);
        }
      }

      .node-content .regeneration-loader {
        background: linear-gradient(135deg,
          var(--regeneration-bg-start-dark, #2E2E2E),
          var(--regeneration-bg-end-dark, #3A3A3A));

        .loader-spinner {
          border-color: var(--regeneration-spinner-bg-dark, #4A4A4A);
          border-top-color: var(--regeneration-spinner-active-dark, #66BB6A);
          box-shadow: 0 2px 8px rgba(102, 187, 106, 0.4);
        }

        .loader-text {
          color: var(--regeneration-text-dark, #81C784);
        }
      }

      .loading-overlay .loading-pulse {
        background: linear-gradient(
          45deg,
          transparent,
          rgba(102, 187, 106, 0.15),
          transparent
        );
      }
    }

    // Enhanced selection for editing state - Dark theme - consolidated selection styling
    &.selected-for-editing {
      border: 3px solid var(--node-selected-editing-border);
      box-shadow:
        0 0 0 6px var(--node-selected-editing-shadow),
        0 16px 40px rgba(0, 0, 0, 0.6);

      &::before {
        background: linear-gradient(45deg, var(--node-selected-editing-border), transparent, var(--node-selected-editing-border));
        opacity: 0.5;
      }

      &::after {
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .node-header {
        background: linear-gradient(135deg, var(--node-title-selected-bg), var(--node-title-selected-bg));

        h4 {
          color: var(--node-title-selected-color);
          background: var(--node-title-selected-bg);
          font-weight: 700;
          padding: 6px 12px;
          border-radius: 6px;
          box-shadow: var(--node-title-selected-shadow);
          margin: 4px;
          display: inline-block;
        }
      }

      &:hover {
        border-color: var(--node-selected-editing-border);
        box-shadow:
          0 0 0 6px var(--node-selected-editing-shadow),
          0 20px 48px rgba(0, 0, 0, 0.7);
      }
    }

    &.dragging {
      box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.4),
        0 6px 16px rgba(0, 0, 0, 0.25);
    }

    .node-header {
      background: var(--design-card-title-bg);
      border-bottom-color: var(--design-card-border);

      h4 {
        color: var(--design-card-title-text);
        transition: all 0.2s ease;
        padding: 4px 8px;
        border-radius: 6px;
        margin: 0;
        font-size: 14px;
        line-height: 1.4;
        letter-spacing: 0.025em;

        // Ensure consistent styling for non-selected nodes
        &:not(.highlighted) {
          background: transparent;
          box-shadow: none;
          text-shadow: none;
        }
      }
    }

    .node-content {
      background: var(--design-card-content-bg);

      .preview-iframe {
        background: var(--design-card-content-bg);
        box-shadow:
          0 2px 6px rgba(0, 0, 0, 0.2),
          0 1px 3px rgba(0, 0, 0, 0.15);
      }

      &:empty::before {
        color: var(--design-card-title-text);
      }

      // Dark theme loader styles
      .node-loader {
        .loader-spinner {
          border-color: var(--design-card-border);
          border-top-color: #60a5fa;
        }

        .loader-text {
          color: var(--design-card-title-text);
        }
      }
    }
  }
}

// ===== UI DESIGN FULL-SCREEN MODAL =====

.ui-design-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);

  // Fullscreen mode styling
  &.modal-fullscreen {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(8px);
  }

  .ui-design-fullscreen-modal {
    background: var(--modal-bg, #ffffff);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 90vw;
    height: 90vh;
    max-width: 1200px;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;

    // Fullscreen mode styling
    &.modal-fullscreen {
      width: 100vw;
      height: 100vh;
      max-width: none;
      max-height: none;
      border-radius: 0;
      box-shadow: none;
    }

    .ui-design-modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      border-bottom: 1px solid var(--modal-border, #e5e7eb);
      background: var(--modal-header-bg, #f8fafc);

      .modal-title h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--modal-title-color, #1f2937);
      }

      .modal-controls {
        display: flex;
        align-items: center;
        gap: 16px;

        .view-mode-toggle {
          display: flex;
          background: var(--toggle-bg, #f3f4f6);
          border-radius: 8px;
          padding: 4px;

          .view-mode-btn {
            background: transparent;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            color: var(--toggle-btn-color, #6b7280);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;

            &.active {
              background: var(--toggle-btn-active-bg, #ffffff);
              color: var(--toggle-btn-active-color, #3b82f6);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            &:hover:not(.active) {
              color: var(--toggle-btn-hover-color, #374151);
            }

            i {
              font-size: 14px;
            }
          }
        }

        // Control buttons container
        .modal-control-buttons {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .control-btn {
          background: var(--close-btn-bg, #f3f4f6);
          border: none;
          border-radius: 8px;
          padding: 8px;
          color: var(--close-btn-color, #6b7280);
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: var(--close-btn-hover-bg, #e5e7eb);
            color: var(--close-btn-hover-color, #374151);
          }

          // Custom SVG icon styling
          .fullscreen-icon,
          .close-icon,
          .new-tab-icon {
            width: 16px;
            height: 16px;
            transition: all 0.2s ease;

            // Apply stroke color to all paths
            path {
              stroke: var(--modal-icon-color, #374151);
              transition: stroke 0.2s ease;
            }
          }

          &:hover {
            .fullscreen-icon path,
            .close-icon path,
            .new-tab-icon path {
              stroke: var(--close-btn-hover-color, #374151);
            }
          }

          // Specific styling for new tab button
          &.new-tab-btn {
            &:hover {
              .new-tab-icon {
                transform: scale(1.1);
              }
            }
          }

          i {
            font-size: 16px;
          }
        }

        // Legacy close button support
        .close-btn {
          background: var(--close-btn-bg, #f3f4f6);
          border: none;
          border-radius: 8px;
          padding: 8px;
          color: var(--close-btn-color, #6b7280);
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: var(--close-btn-hover-bg, #e5e7eb);
            color: var(--close-btn-hover-color, #374151);
          }

          i {
            font-size: 16px;
          }
        }
      }
    }

    .ui-design-modal-content {
      flex: 1;
      padding: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--modal-content-bg, #f8fafc);

      .ui-design-preview-container {
        background: var(--preview-container-bg, #ffffff);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;

        &.mobile-view {
          width: 375px;
          height: 667px;
          max-height: 80vh;
        }

        &.web-view {
          width: 100%;
          height: 100%;
          max-width: 1024px;
          max-height: 80vh;
        }

        .ui-design-preview-iframe {
          width: 100%;
          height: 100%;
          border: none;
        }
      }
    }
  }
}

// ===== DARK THEME SUPPORT FOR UI DESIGN MODAL =====

:host-context(.dark-theme) {
  .ui-design-fullscreen-overlay {
    background: rgba(0, 0, 0, 0.9);

    .ui-design-fullscreen-modal {
      --modal-bg: #1f2937;
      --modal-border: #374151;
      --modal-header-bg: #111827;
      --modal-title-color: #f9fafb;
      --modal-content-bg: #111827;

      // Toggle button styles
      --toggle-bg: #374151;
      --toggle-btn-color: #9ca3af;
      --toggle-btn-hover-color: #d1d5db;
      --toggle-btn-active-bg: #1f2937;
      --toggle-btn-active-color: #60a5fa;

      // Close button styles
      --close-btn-bg: #374151;
      --close-btn-color: #9ca3af;
      --close-btn-hover-bg: #4b5563;
      --close-btn-hover-color: #f3f4f6;

      // Preview container styles
      --preview-container-bg: #1f2937;

      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);

      .ui-design-modal-content {
        .ui-design-preview-container {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
          border: 1px solid #374151;
        }
      }
    }
  }
}

// ===== CANVAS SELECTION TOOLTIP STYLES =====

.canvas-selection-tooltip {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  pointer-events: none;
  animation: fadeInTooltip 0.3s ease-in-out;

  .tooltip-content {
    background: var(--tooltip-bg);
    border: 1px solid var(--tooltip-border);
    border-radius: 6px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    i {
      color: var(--tooltip-icon);
      font-size: 12px;
    }

    span {
      color: var(--tooltip-text);
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;
    }
  }

  // Light theme specific styles
  &.light-theme {
    .tooltip-content {
      background: var(--tooltip-bg);
      border-color: var(--tooltip-border);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      i {
        color: var(--tooltip-icon);
      }

      span {
        color: var(--tooltip-text);
      }
    }
  }

  // Dark theme specific styles
  &.dark-theme {
    .tooltip-content {
      background: var(--tooltip-bg);
      border-color: var(--tooltip-border);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      i {
        color: var(--tooltip-icon);
      }

      span {
        color: var(--tooltip-text);
      }
    }
  }
}

@keyframes fadeInTooltip {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// ===== MULTI-SELECTION STYLES =====

// Multi-selection overlay for selected nodes - consolidated to single class
.ui-design-node.selected-for-editing {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--multi-select-overlay-bg);
    border: 2px solid var(--multi-select-border);
    border-radius: 12px;
    pointer-events: none;
    z-index: 1;
    animation: multiSelectPulse 2s ease-in-out infinite;
  }

  // Selection label showing page name
  &::after {
    content: attr(data-page-name);
    position: absolute;
    top: -8px;
    left: 8px;
    background: var(--multi-select-label-bg);
    color: var(--multi-select-label-text);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    z-index: 2;
    pointer-events: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  // Enhanced visual feedback with title highlighting
  .node-header {
    background: linear-gradient(135deg, var(--design-card-title-bg), var(--multi-select-overlay-bg));

    h4 {
      color: var(--node-title-selected-color);
      background: var(--node-title-selected-bg);
      font-weight: 700;
      padding: 4px 8px;
      border-radius: 6px;
      box-shadow: var(--node-title-selected-shadow);
      transition: all 0.2s ease;
      display: inline-block;
      margin: 0;
      animation: titleHighlight 0.6s ease-out;

      // Enhanced text visibility with subtle text shadow
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      // Ensure proper contrast and readability
      font-size: 14px;
      line-height: 1.4;
      letter-spacing: 0.025em;

      // Accessibility improvements
      &:focus-visible {
        outline: var(--node-title-focus-outline);
        outline-offset: 2px;
      }

      // Screen reader support
      &::before {
        content: "Selected: ";
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      }
    }
  }

  // Hover state for multi-selected nodes
  &:hover {
    &::before {
      background: var(--multi-select-overlay-bg);
      border-color: var(--multi-select-border);
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
    }

    // Enhanced hover effect for highlighted title
    .node-header h4 {
      color: var(--node-title-selected-color);
      background: var(--node-title-selected-bg);
      box-shadow: var(--node-title-selected-shadow), 0 2px 4px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }
  }
}

// Selection controls container
.selection-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--selection-controls-bg);
  border: 1px solid var(--selection-controls-border);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .selection-btn {
    background: var(--selection-btn-bg);
    border: 1px solid var(--selection-controls-border);
    border-radius: 6px;
    padding: 6px 12px;
    color: var(--canvas-btn-color);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;

    &:hover:not(:disabled) {
      background: var(--selection-btn-hover-bg);
      border-color: var(--canvas-btn-hover-border);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      background: var(--selection-btn-disabled-bg);
      color: var(--selection-btn-disabled-color);
      cursor: not-allowed;
      opacity: 0.6;
    }

    i {
      font-size: 12px;
    }
  }

  .selection-info {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--selection-info-bg);
    border-radius: 4px;

    .selection-count {
      color: var(--selection-info-color);
      font-size: 11px;
      font-weight: 600;
    }
  }
}

// Multi-selection pulse animation
@keyframes multiSelectPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.002);
  }
}

// Title highlight animation for newly selected nodes
@keyframes titleHighlight {
  0% {
    background: transparent;
    box-shadow: none;
    transform: scale(1);
  }
  50% {
    background: var(--node-title-selected-bg);
    box-shadow: var(--node-title-selected-shadow), 0 0 8px rgba(59, 130, 246, 0.3);
    transform: scale(1.02);
  }
  100% {
    background: var(--node-title-selected-bg);
    box-shadow: var(--node-title-selected-shadow);
    transform: scale(1);
  }
}

// Enhanced spinning animation for preview tab
.spinning {
  animation: spin 1s linear infinite;
}

// Preview error container and retry functionality
.preview-error-container {
  position: relative;
  height: 100%;

  .preview-error-actions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;

    .retry-preview-btn {
      background: var(--awe-primary-color, #007bff);
      color: white;
      border: none;
      border-radius: 6px;
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);

      &:hover:not(:disabled) {
        background: var(--awe-primary-hover, #0056b3);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      i {
        font-size: 16px;

        &.spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

// Standalone title highlighting class for better control
.ui-design-node .node-header h4.highlighted-title {
  color: var(--node-title-selected-color) !important;
  background: var(--node-title-selected-bg) !important;
  box-shadow: var(--node-title-selected-shadow) !important;
  font-weight: 700 !important;
  animation: titleHighlight 0.6s ease-out;

  // Ensure proper spacing and layout
  padding: 4px 8px;
  border-radius: 6px;
  margin: 0;
  display: inline-block;
  transition: all 0.2s ease;

  // Enhanced accessibility
  &:focus-visible {
    outline: var(--node-title-focus-outline);
    outline-offset: 2px;
  }

  // Hover enhancement
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--node-title-selected-shadow), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

// Enhanced canvas controls layout for multi-selection
.canvas-controls {
  .view-controls {
    margin-right: 16px;
  }

  .selection-controls {
    margin-left: auto;
  }
}

// Responsive adjustments for selection controls
@media (max-width: 768px) {
  .selection-controls {
    flex-wrap: wrap;
    gap: 8px;

    .selection-btn {
      padding: 4px 8px;
      font-size: 11px;

      i {
        font-size: 11px;
      }
    }

    .selection-info {
      .selection-count {
        font-size: 10px;
      }
    }
  }
}
