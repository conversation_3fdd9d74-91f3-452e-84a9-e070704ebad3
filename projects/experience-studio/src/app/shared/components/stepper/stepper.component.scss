// Use utility classes from padding.scss (imported globally)
// Note: padding.scss utility classes are available globally

// CSS Variables for theming
:root {
  --stepper-primary: #6b46c1;
  --stepper-primary-light: #9f7aea;
  --stepper-success: #10b981;
  --stepper-border-radius: 8px;
  --stepper-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stepper {
  width: 100%;
  margin: 1rem auto;
  border-radius: var(--stepper-border-radius);
  opacity: 1;
  transform: translateY(0);

  &.light,
  &.dark {
    background-color: var(--chat-window-card-bg-color) !important;
    box-shadow: none;
    backdrop-filter: blur(60px);
  }

  // Stepper item container
  .stepper-item {
    width: 100%;
    will-change: transform, opacity, height;
    transform: translateZ(0);
    transition: var(--stepper-transition);

    &.hidden { display: none; }

    &.future {
      opacity: 0;
      height: 0;
      transform: translateY(-20px);
      overflow: hidden;
    }

    &.processing {
      opacity: 1;
      pointer-events: auto;
    }

    &.completed {
      opacity: 0.8;
    }
  }

  // Step connecting line
  .step-line-container {
    left: 11px;
    top: 30px;
    width: 2px;
    height: calc(100% - 30px);
    z-index: 1;
    transition: var(--stepper-transition);

    &.hidden-line { opacity: 0; }
  }

  .step-line {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, var(--stepper-primary) 0%, var(--stepper-primary-light) 100%);
    border-radius: 1px;
    transition: var(--stepper-transition);

    &.completed {
      background: linear-gradient(180deg, var(--stepper-success) 0%, var(--stepper-primary) 100%);
    }
  }

  // Step circle/icon
  .step-circle {
    z-index: 10;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: var(--stepper-transition);

    .light & {
      border: none;
      background-color: #f8f9fa;
    }

    .dark & {
      background-color: #333;
      border: none;
    }

    &.completed,
    &.active {
      background-color: var(--stepper-primary) !important;
      color: white;
      box-shadow: 0 2px 8px rgba(107, 70, 193, 0.3);
    }

    &.processing {
      background-color: var(--stepper-primary) !important;
      color: white;
    }

    &.clickable {
      cursor: pointer !important;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(107, 70, 193, 0.4);
      }
    }

    &.non-clickable {
      cursor: default !important;
      pointer-events: none !important;
    }
  }

  // Step icons and animations
  .step-icon {
    width: 14px;
    height: 14px;
    color: white;
  }

  .step-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .step-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    opacity: 0.6;
  }

  // Step content area
  .step-content {
    min-width: 0; // Prevent flex item overflow
  }

  .step-card {
    background-color: transparent;
    border-radius: var(--stepper-border-radius);
    padding: 0;
  }

  // Step header
  .step-header {
    margin-bottom: 0.5rem;
  }

  .step-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0;
    cursor: pointer;
    transition: var(--stepper-transition);

    .light & { color: var(--text-primary, #2d3748); }
    .dark & { color: var(--text-primary-dark, #f8f9fa); }

    &:hover:not(.non-clickable) {
      .light & { color: var(--stepper-primary); }
      .dark & { color: var(--stepper-primary-light); }
    }

    &.non-clickable {
      cursor: default !important;
      pointer-events: none !important;
    }

    &.typing {
      .step-title-text::after {
        content: '|';
        animation: blink 1s infinite;
      }
    }
  }

  // Timer display
  .step-timer {
    font-size: 0.875rem;
    opacity: 0.7;

    .light & { color: var(--text-secondary, #4a5568); }
    .dark & { color: var(--text-secondary-dark, #cbd5e0); }
  }

  .timer-icon {
    width: 16px;
    height: 16px;
  }

  // Step description
  .step-description {
    font-size: 0.875rem;
    max-height: 1000px;
    opacity: 1;
    overflow: hidden;
    will-change: max-height, opacity, margin;
    transform: translateZ(0);
    transition: var(--stepper-transition);

    .light & { color: var(--text-secondary, #4a5568); }
    .dark & { color: var(--text-secondary-dark, #cbd5e0); }

    &.collapsed {
      max-height: 0;
      opacity: 0;
      margin: 0;
    }

    // Markdown content styling
    ::ng-deep {
      p { margin: 0 0 0.5rem 0; }
      ul, ol {
        margin: 0.5rem 0;
        padding-left: 1.25rem;
      }
      code {
        background-color: rgba(107, 70, 193, 0.1);
        padding: 0.125rem 0.25rem;
        border-radius: 4px;
        font-size: 0.8em;
      }
      pre {
        background-color: rgba(107, 70, 193, 0.05);
        padding: 0.75rem;
        border-radius: var(--stepper-border-radius);
        overflow-x: auto;
        margin: 0.5rem 0;
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

// Responsive design
@media (max-width: 768px) {
  .stepper {
    margin: 0.5rem auto;

    .step-circle {
      width: 20px;
      height: 20px;
      margin-right: 0.75rem;
    }

    .step-line-container {
      left: 9px;
    }

    .step-title {
      font-size: 1rem;
    }

    .step-description {
      font-size: 0.8rem;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .stepper {
    .step-circle {
      border: 2px solid currentColor;
    }

    .step-line {
      background: currentColor;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .stepper {
    .step-circle,
    .step-description,
    .stepper-item {
      transition: none;
    }

    .step-spinner {
      animation: none;
    }
  }
}
