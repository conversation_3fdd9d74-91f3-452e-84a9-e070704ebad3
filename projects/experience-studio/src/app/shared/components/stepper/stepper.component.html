<!-- Stepper container - only show when ready -->
<div class="stepper" [ngClass]="theme" *ngIf="isStepperReady" [@stepperFadeIn]>
  <div class="d-flex flex-column g-4">
    <!-- Step item -->
    <div 
      class="stepper-item d-flex position-relative"
      *ngFor="let step of steps; let i = index; let isLast = last"
      [ngClass]="getStepClasses(i)"
      [class.hidden]="!shouldShowStep(i)"
      [@stepSlideIn]="getStepAnimationState(i)">

      <!-- Step connecting line -->
      <div 
        class="step-line-container position-absolute" 
        *ngIf="!isLast" 
        [class.hidden-line]="isStepCollapsed(i)">
        <div class="step-line" [class.completed]="step.completed"></div>
      </div>

      <!-- Step circle/icon -->
      <div 
        class="step-circle d-flex align-items-center justify-content-center flex-shrink-0 position-relative"
        [ngClass]="getCircleClasses(step, i)" 
        (click)="!isProcessingStep(i) ? toggleStepCollapse(i) : null">
        
        <!-- Completed state icon -->
        <svg *ngIf="step.completed" class="step-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
        </svg>
        
        <!-- Processing state spinner -->
        <div *ngIf="step.active && !step.completed" class="step-spinner"></div>
        
        <!-- Default state dot -->
        <div *ngIf="!step.active && !step.completed" class="step-dot"></div>
      </div>

      <!-- Step content -->
      <div class="step-content flex-grow-1">
        <div class="step-card">
          <!-- Step header with title and timer -->
          <div class="step-header d-flex justify-content-between align-items-center">
            <h3 
              class="step-title mb-2 cursor-pointer"
              [class.typing]="step.isTitleTyping" 
              (click)="!isProcessingStep(i) ? toggleStepCollapse(i) : null"
              [class.non-clickable]="isProcessingStep(i)">
              <span class="step-title-text text-semibold">{{ getStepTitle(step) }}</span>
            </h3>
            
            <!-- Timer display -->
            <div 
              class="step-timer d-flex align-items-center"
              *ngIf="step.timerActive && step.elapsedTime !== undefined">
              <svg class="timer-icon me-1" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15,1H9V3H15M11,14H13V8H11M19.03,7.39L20.45,5.97C20,5.46 19.55,5 19.04,4.56L17.62,6C16.07,4.74 14.12,4 12,4A9,9 0 0,0 3,13A9,9 0 0,0 12,22C17,22 21,17.97 21,13C21,10.88 20.26,8.93 19.03,7.39Z"/>
              </svg>
              <span class="timer-text">{{ formatElapsedTime(step.elapsedTime) }}</span>
            </div>
          </div>

          <!-- Step description with expand/collapse animation -->
          <div 
            class="step-description"
            [ngClass]="{ 'collapsed': isStepCollapsed(i) }"
            [@stepContentExpand]="getContentExpandState(step, i)">
            <markdown [data]="getStepDescription(step)"></markdown>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
