import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  OnInit,
  OnDestroy,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  inject,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MarkdownModule } from 'ngx-markdown';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { StepperState, StepperStateDisplayTitles } from '../../models/stepper-states.enum';
import { PollingService } from '../../services/polling.service';
import { ContentSanitizationService } from '../../services/content-sanitization.service';

export interface StepperItem {
  title: string;
  description: string;
  visibleTitle: string;
  visibleDescription: string;
  completed: boolean;
  active: boolean;
  collapsed?: boolean;
  isTyping?: boolean;
  isTitleTyping?: boolean;
  retryCount?: number;
  isRetrying?: boolean;
  startTime?: number;
  elapsedTime?: number;
  timerActive?: boolean;
}

@Component({
  selector: 'app-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('stepperFadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('600ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('stepSlideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-30px)', height: '0px' }),
        animate('800ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ opacity: 1, transform: 'translateX(0)', height: '*' }))
      ])
    ]),
    trigger('stepContentExpand', [
      state('expanded', style({
        maxHeight: '*',
        opacity: 1,
        marginBottom: '0.5rem',
        overflow: 'visible'
      })),
      state('collapsed', style({
        maxHeight: '0px',
        opacity: 0,
        marginBottom: '0px',
        overflow: 'hidden'
      })),
      transition('expanded <=> collapsed', [
        animate('500ms cubic-bezier(0.4, 0, 0.2, 1)')
      ])
    ])
  ]
})
export class StepperComponent implements OnChanges, OnInit, OnDestroy {
  @Input() progress: string = '';
  @Input() progressDescription: string = '';
  @Input() status: string = 'PENDING';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() restartable: boolean = false;
  @Input() projectId: string = '';
  @Input() jobId: string = '';
  @Input() useApi: boolean = false;
  @Output() stepUpdated = new EventEmitter<number>();
  @Output() retryStep = new EventEmitter<number>();

  private destroyRef = inject(DestroyRef);
  private cdr = inject(ChangeDetectorRef);
  private pollingService = inject(PollingService);
  private contentSanitizationService = inject(ContentSanitizationService);

  steps: StepperItem[] = [];
  currentStep: StepperItem | null = null;
  currentStepIndex: number = 0;
  isStepperReady: boolean = false;

  private timeoutRefs: { [key: string]: any } = {};
  private timerInterval: any;
  private collapsedSteps: Set<number> = new Set();
  private stepperStateMap = StepperStateDisplayTitles;
  public StepperState = StepperState;

  ngOnInit(): void {
    this.isStepperReady = false;

    if (this.useApi && this.projectId && this.jobId) {
      this.startApiPolling();
    } else if (this.steps.length === 0 && this.progress) {
      this.initializeFirstStep();
    }

    this.initializeExistingSteps();
  }

  ngOnChanges(): void {
    if (!this.useApi) {
      this.updateStepper();
    }
  }

  ngOnDestroy(): void {
    this.clearAllTimeouts();
    this.stopTimer();
    if (this.useApi) {
      this.stopApiPolling();
    }
  }

  // API Polling Methods
  private startApiPolling(): void {
    this.pollingService.startPolling(this.projectId, this.jobId);

    this.pollingService.progress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((progress: string) => {
        if (progress) {
          this.progress = progress;
          this.updateStepper();
          if (!this.isStepperReady) {
            this.isStepperReady = true;
          }
          this.cdr.markForCheck();
        }
      });

    this.pollingService.progressDescription$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((description: string) => {
        if (description) {
          this.progressDescription = description;
          this.updateStepper();
          this.cdr.markForCheck();
        }
      });

    this.pollingService.status$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((status: string) => {
        if (status) {
          this.status = status;
          this.updateStepper();
          this.cdr.markForCheck();
        }
      });
  }

  private stopApiPolling(): void {
    this.pollingService.stopPolling();
  }

  // Core Stepper Logic
  private initializeFirstStep(): void {
    const step: StepperItem = {
      title: this.getDisplayTitleForProgress(this.progress),
      description: this.formatDescription(this.progressDescription) || 'Starting process...',
      visibleTitle: '',
      visibleDescription: '',
      completed: false,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: true,
    };

    this.steps.push(step);
    this.currentStep = this.steps[0];
    this.currentStepIndex = 0;

    if (this.status === 'IN_PROGRESS') {
      this.collapsedSteps.delete(this.currentStepIndex);
    }

    this.isStepperReady = true;
    this.cdr.markForCheck();

    this.stepUpdated.emit(this.currentStepIndex);
    setTimeout(() => this.startTypewriterAnimation(this.currentStepIndex), 300);
    this.startTimer();
  }

  private initializeExistingSteps(): void {
    if (this.steps.length > 0) {
      this.isStepperReady = true;
      this.cdr.markForCheck();
    }

    this.steps.forEach((step, i) => {
      if (!step.visibleDescription || !step.visibleTitle) {
        Object.assign(step, {
          visibleTitle: step.visibleTitle || '',
          visibleDescription: step.visibleDescription || '',
          isTitleTyping: true,
          isTyping: true,
        });
        setTimeout(() => this.startTypewriterAnimation(i), 200 * (i + 1));
      }
    });
  }

  private updateStepper(): void {
    if (!this.progress) return;

    const displayTitle = this.getDisplayTitleForProgress(this.progress);
    const existingStepIndex = this.steps.findIndex(step =>
      step.title === displayTitle || this.getDisplayTitleForProgress(step.title) === displayTitle
    );

    if (this.status === 'FAILED' || this.status === 'failed') {
      this.handleFailedStep(this.progress, this.progressDescription);
      return;
    }

    if (existingStepIndex === -1) {
      this.addNewStep(displayTitle);
    } else {
      this.updateExistingStep(existingStepIndex, displayTitle);
    }
  }

  private addNewStep(displayTitle: string): void {
    const newStep = this.createStepItem(displayTitle);

    if (this.steps.length > 0) {
      this.completePreviousStep();
    } else {
      this.isStepperReady = true;
      this.cdr.markForCheck();
    }

    this.steps.push(newStep);
    this.setCurrentStep(newStep, this.steps.length - 1);
    this.startStepAnimations();
  }

  private createStepItem(displayTitle: string): StepperItem {
    return {
      title: displayTitle,
      description: this.formatDescription(this.progressDescription) || '',
      visibleTitle: '',
      visibleDescription: '',
      completed: false,
      active: true,
      isTitleTyping: true,
      isTyping: true,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: true
    };
  }

  private completePreviousStep(): void {
    if (this.currentStep) {
      this.currentStep.completed = true;
      this.currentStep.active = false;
      this.currentStep.timerActive = false;
    }
  }

  private setCurrentStep(step: StepperItem, index: number): void {
    this.currentStep = step;
    this.currentStepIndex = index;
    this.collapsedSteps.delete(index);
  }

  private startStepAnimations(): void {
    this.stepUpdated.emit(this.currentStepIndex);
    setTimeout(() => this.startTypewriterAnimation(this.currentStepIndex), 100);
    this.startTimer();
  }

  // Utility Methods
  private getDisplayTitleForProgress(progress: string): string {
    return this.stepperStateMap[progress as keyof typeof StepperStateDisplayTitles] || progress;
  }

  private formatDescription(description: string): string {
    return this.contentSanitizationService.preprocessStepperDescription(description || '');
  }

  // Animation and Interaction Methods
  getStepAnimationState(index: number): string {
    return this.shouldShowStep(index) ? 'visible' : 'hidden';
  }

  getContentExpandState(_step: StepperItem, index: number): string {
    return this.isStepCollapsed(index) ? 'collapsed' : 'expanded';
  }

  shouldShowStep(index: number): boolean {
    return index <= this.currentStepIndex;
  }

  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  isProcessingStep(index: number): boolean {
    return this.status === 'IN_PROGRESS' && index === this.currentStepIndex;
  }

  toggleStepCollapse(index: number): void {
    if (this.isProcessingStep(index)) return;

    const isCurrentlyCollapsed = this.collapsedSteps.has(index);

    if (isCurrentlyCollapsed) {
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== index && !this.isProcessingStep(i)) {
          this.collapsedSteps.add(i);
        }
      }
      this.collapsedSteps.delete(index);
    } else {
      this.collapsedSteps.add(index);
    }

    this.cdr.markForCheck();
  }

  // Helper methods for template
  getStepClasses(index: number): string {
    const step = this.steps[index];
    const classes = [];

    if (step?.completed) classes.push('completed');
    if (step?.active) classes.push('active');
    if (this.isProcessingStep(index)) classes.push('processing');

    return classes.join(' ');
  }

  getCircleClasses(step: StepperItem, index: number): string {
    const classes = [];

    if (step.completed) classes.push('completed');
    if (step.active) classes.push('active');
    if (this.isProcessingStep(index)) classes.push('processing', 'non-clickable');
    else classes.push('clickable');

    return classes.join(' ');
  }

  getStepTitle(step: StepperItem): string {
    return step.visibleTitle || step.title;
  }

  getStepDescription(step: StepperItem): string {
    return step.visibleDescription || step.description;
  }

  // Cleanup
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeout => clearTimeout(timeout));
    this.timeoutRefs = {};
  }

  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  // Placeholder methods for complex functionality (to be implemented)
  private startTypewriterAnimation(index: number): void {
    // Simplified typewriter animation
    const step = this.steps[index];
    if (!step) return;

    step.visibleTitle = step.title;
    step.visibleDescription = step.description;
    step.isTitleTyping = false;
    step.isTyping = false;
    this.cdr.markForCheck();
  }

  private startTimer(): void {
    // Simplified timer implementation
    this.timerInterval = setInterval(() => {
      if (this.currentStep?.timerActive) {
        const elapsed = Math.floor((Date.now() - (this.currentStep.startTime || 0)) / 1000);
        this.currentStep.elapsedTime = elapsed;
        this.cdr.markForCheck();
      }
    }, 1000);
  }

  private handleFailedStep(_progress: string, description: string): void {
    // Simplified failure handling
    if (this.currentStep) {
      this.currentStep.completed = false;
      this.currentStep.active = false;
      this.currentStep.description = description || 'Step failed';
      this.cdr.markForCheck();
    }
  }

  private updateExistingStep(index: number, _title: string): void {
    // Simplified existing step update
    const step = this.steps[index];
    if (step) {
      step.description = this.formatDescription(this.progressDescription) || step.description;
      this.cdr.markForCheck();
    }
  }

  formatElapsedTime(elapsedTime: number): string {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  public resetStepper(): void {
    this.clearAllTimeouts();
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.collapsedSteps.clear();
    this.progress = '';
    this.progressDescription = '';
    this.status = 'PENDING';
    this.isStepperReady = false;

    if (this.useApi) {
      this.stopApiPolling();
    }

    this.stepUpdated.emit(-1);
    this.cdr.markForCheck();
  }
}
